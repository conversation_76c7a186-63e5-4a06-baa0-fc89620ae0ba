import 'dart:convert';
import 'dart:io';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class MyNotification extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _MyNotification();
  }
}

class _MyNotification extends State<MyNotification> {
  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      getNotifications();
    });
  }

  List notifications = [];

  getNotifications() async {
    try {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
      String id = sharedPreferences.getString('id').toString();

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.NOTIFICATIONS),
        headers: {
          'Accept': 'application/json',
        },
        body: {'id': id},
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        setState(() {
          notifications = json['data'];
        });
      }
    } on HandshakeException catch (e) {
      getNotifications();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          'Notifikasi',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20)
            .copyWith(bottom: 30),
        child: Column(
          children: notifications.map(
            (e) {
              return Container(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    InkWell(
                      child: Container(
                        padding: EdgeInsets.all(8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                color: primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.account_circle_outlined,
                                  color: Colors.white,
                                  size: 18,
                                ),
                              ),
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    e['title'],
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 4,
                                  ),
                                  Text(
                                    e['body'],
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).pop({
                          'type': e['type'] ?? '',
                        });
                      },
                    ),
                    Divider()
                  ],
                ),
              );
            },
          ).toList(),
        ),
      ),
    );
  }
}

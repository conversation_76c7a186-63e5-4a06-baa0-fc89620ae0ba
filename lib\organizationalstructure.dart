import 'dart:convert';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class MyOrganizationalStructure extends StatefulWidget {
  const MyOrganizationalStructure({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MyOrganizationalStructure();
  }
}

class _MyOrganizationalStructure extends State<MyOrganizationalStructure> {
  List organizationalstructure = [];

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      getOrganizationalStructure();
    });
  }

  getOrganizationalStructure() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(
      Uri.parse(API.ORGANIZATIONALSTRUCTUREGET),
      headers: {
        'Accept': 'application/json',
      },
      body: {
        'userid': id,
      },
      encoding: Encoding.getByName('UTF-8'),
    );

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      setState(() {
        organizationalstructure = json['data'];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          backgroundColor: primaryColor,
          centerTitle: true,
          title: const Text(
            'Struktur Organisasi',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          leading: IconButton(
            icon: const Icon(
              CupertinoIcons.back,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          )),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20)
            .copyWith(bottom: 25, top: 20),
        child: Column(
          children: organizationalstructure.map((e) {
            return Container(
              padding: const EdgeInsets.all(10).copyWith(left: 20, right: 20),
              margin: const EdgeInsets.only(bottom: 10),
              decoration: BoxDecoration(
                color: const Color(0xFFEDEDED),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                children: [
                  ClipOval(
                    child: SizedBox(
                      width: 75,
                      height: 75,
                      child: e['images'] == null || e['images'] == ''
                          ? Image.asset(
                              'images/profile.png',
                              width: 75,
                              height: 75,
                              fit: BoxFit.cover,
                            )
                          : Image.network(
                              '${API.GOOGLEAPIS}/${e['images']}',
                              width: 75,
                              height: 75,
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        e['name'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      // Text(
                      //   'NIP. ${e['nip']}',
                      //   style: const TextStyle(
                      //     fontSize: 10,
                      //     fontWeight: FontWeight.w400,
                      //   ),
                      // ),
                      Text(
                        e['position'],
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

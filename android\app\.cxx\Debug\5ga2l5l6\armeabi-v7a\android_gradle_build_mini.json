{"buildFiles": ["C:\\Users\\<USER>\\AppData\\Local\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Android Project\\siades\\android\\app\\.cxx\\Debug\\5ga2l5l6\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Android Project\\siades\\android\\app\\.cxx\\Debug\\5ga2l5l6\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
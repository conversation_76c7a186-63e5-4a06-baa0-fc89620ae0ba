// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAYf6Kn0RzR3W4c6ROL2H3zZ4hY-afLF8A',
    appId: '1:643989139003:web:25bb5d8150a17d0e55761e',
    messagingSenderId: '643989139003',
    projectId: 'absensi-asn',
    authDomain: 'absensi-asn.firebaseapp.com',
    databaseURL: 'https://absensi-asn-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'absensi-asn.firebasestorage.app',
    measurementId: 'G-XW0E9G944D',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCtSIF-0pwaD6XvxTXUbHAM5nVYcI8oArU',
    appId: '1:643989139003:android:7b2946bc908cb97a55761e',
    messagingSenderId: '643989139003',
    projectId: 'absensi-asn',
    databaseURL: 'https://absensi-asn-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'absensi-asn.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDKy-pbIrOgTBu2avtY0sG5L9t6-q41mX8',
    appId: '1:643989139003:ios:e42a8123649efce855761e',
    messagingSenderId: '643989139003',
    projectId: 'absensi-asn',
    databaseURL: 'https://absensi-asn-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'absensi-asn.firebasestorage.app',
    iosBundleId: 'com.siades',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDKy-pbIrOgTBu2avtY0sG5L9t6-q41mX8',
    appId: '1:643989139003:ios:e33d696d922031ac55761e',
    messagingSenderId: '643989139003',
    projectId: 'absensi-asn',
    databaseURL: 'https://absensi-asn-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'absensi-asn.firebasestorage.app',
    iosBundleId: 'com.example.siades',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCwcHYzYRsHLcP6GVdn_2ORkAXRdvySdD8',
    appId: '1:643989139003:web:e0a24678e90e3af555761e',
    messagingSenderId: '643989139003',
    projectId: 'absensi-asn',
    authDomain: 'absensi-asn.firebaseapp.com',
    databaseURL: 'https://absensi-asn-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'absensi-asn.firebasestorage.app',
    measurementId: 'G-XBBX591TQ0',
  );

}
{"project_info": {"project_number": "643989139003", "firebase_url": "https://absensi-asn-default-rtdb.asia-southeast1.firebasedatabase.app", "project_id": "absensi-asn", "storage_bucket": "absensi-asn.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:643989139003:android:52499c17150f749655761e", "android_client_info": {"package_name": "com.example.absensi_asn"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyCtSIF-0pwaD6XvxTXUbHAM5nVYcI8oArU"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:643989139003:android:593ff61672326a6255761e", "android_client_info": {"package_name": "com.konex"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyCtSIF-0pwaD6XvxTXUbHAM5nVYcI8oArU"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:643989139003:android:bb6eba8b9a5585d655761e", "android_client_info": {"package_name": "com.senusa.attendancesystem"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyCtSIF-0pwaD6XvxTXUbHAM5nVYcI8oArU"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:643989139003:android:7b2946bc908cb97a55761e", "android_client_info": {"package_name": "com.siades"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyCtSIF-0pwaD6XvxTXUbHAM5nVYcI8oArU"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}
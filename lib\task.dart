import 'dart:convert';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class MyTask extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _MyTaskState();
  }
}

class _MyTaskState extends State<MyTask> {
  List data = [];
  String filter = "Semua";

  getTask() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(Uri.parse(API.TASKGET),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': id,
          'status': filter,
        },
        encoding: Encoding.getByName('UTF-8'));

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      setState(() {
        data = json['data'];
      });
    } else {
      showAlertFailed(context, json['message']);
    }
  }

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      getTask();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: primaryColor,
        centerTitle: true,
        title: const Text(
          'Daftar Penugasan',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(
            CupertinoIcons.back,
            color: Colors.white,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Container(
        padding: EdgeInsets.all(20),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(bottom: 15),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    'Semua',
                    'Belum Dikerjakan',
                    'Sedang Dikerjakan',
                    'Menunggu Verifikasi',
                    'Ditolak',
                    'Selesai'
                  ].map(
                    (e) {
                      return InkWell(
                        child: Container(
                          padding:
                              EdgeInsets.all(5).copyWith(left: 15, right: 15),
                          margin: EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            color: filter == e ? primaryColor : Colors.white,
                            borderRadius: BorderRadius.circular(5),
                            border: Border.all(
                              color: filter == e ? primaryColor : Colors.grey,
                            ),
                          ),
                          child: Text(
                            e,
                            style: TextStyle(
                              color:
                                  filter == e ? Colors.white : Colors.black87,
                            ),
                          ),
                        ),
                        onTap: () {
                          setState(() {
                            filter = e;
                          });

                          getTask();
                        },
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            SizedBox(
              height: MediaQuery.of(context).size.height * .8,
              child: SingleChildScrollView(
                child: data.isNotEmpty
                    ? Column(
                        children: data.map(
                          (e) {
                            String status = "";
                            Color bgColor = Color(0xFF0077c1);

                            if (e['status'] == 'Process') {
                              status = "Belum Dikerjakan";
                              bgColor = Color(0xEEe67800);
                            } else if (e['status'] == 'Processing') {
                              status = "Sedang Dikerjakan";
                              bgColor = Color(0xFF00abfa);
                            } else if (e['status'] == 'Pending') {
                              status = "Menunggu Verifikasi";
                              bgColor = Color(0xFF00abfa);
                            } else if (e['status'] == 'Reject') {
                              status = "Ditolak";
                              bgColor = Color(0xFFff4555);
                            } else {
                              status = "Selesai";
                              bgColor = primaryColor;
                            }

                            return Container(
                              padding: EdgeInsets.all(20),
                              margin: EdgeInsets.only(bottom: 10),
                              decoration: BoxDecoration(
                                color: bgColor,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: InkWell(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(.25),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        CupertinoIcons.doc,
                                        color: Colors.white,
                                        size: 30,
                                      ),
                                    ),
                                    SizedBox(width: 15),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            e['task'],
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          SizedBox(height: 5),
                                          Text(
                                            e['description'],
                                            style: TextStyle(
                                              color: Colors.white,
                                            ),
                                            maxLines: 3,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          SizedBox(height: 10),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Container(
                                                padding: EdgeInsets.all(5),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                ),
                                                child: Text(
                                                  '${DateFormat('d MMM yyyy').format(DateTime.parse(e['starttask']))} s/d ${DateFormat('d MMM yyyy').format(DateTime.parse(e['deadline']))}',
                                                  style: TextStyle(
                                                    fontSize: 10,
                                                    color: bgColor,
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                child: Text(
                                                  status,
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                  textAlign: TextAlign.end,
                                                ),
                                              )
                                            ],
                                          )
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                                onTap: () {
                                  Fluttertoast.showToast(
                                      msg:
                                          'Silahkan melakukan penugasan melalui website SiapKada di https://siapkada.id/');
                                },
                              ),
                            );
                          },
                        ).toList(),
                      )
                    : Container(
                        margin: const EdgeInsets.only(top: 40),
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'images/no-data.png',
                              width: 100,
                            ),
                            const SizedBox(height: 10),
                            const Text(
                              'Tidak ada data',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF555555),
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

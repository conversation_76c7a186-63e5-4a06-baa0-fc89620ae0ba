import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:siades/absence.dart';
import 'package:siades/addactivity.dart';
import 'package:siades/api.dart';
import 'package:siades/available_member.dart';
import 'package:siades/color.dart';
import 'package:siades/cuti.dart';
import 'package:siades/event.dart';
import 'package:siades/historyactivity.dart';
import 'package:siades/listofattendance.dart';
import 'package:siades/main.dart';
import 'package:siades/modal.dart';
import 'package:siades/news.dart';
import 'package:siades/news_all.dart';
import 'package:siades/notification.dart';
import 'package:siades/organizationalstructure.dart';
import 'package:siades/task.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:detect_fake_location/detect_fake_location.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:qr_code_dart_scan/qr_code_dart_scan.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:http/http.dart' as http;
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

class MyDashboard extends StatefulWidget {
  const MyDashboard({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MyDashboard();
  }
}

class AbsenceTrack {
  final String absencestatus;

  const AbsenceTrack(this.absencestatus);

  @override
  String toString() => absencestatus;
}

class _MyDashboard extends State<MyDashboard> {
  var timer;

  String? _timeString;
  final CarouselSliderController? _sliderController =
      CarouselSliderController();
  SharedPreferences? sharedPreferences;
  final _noScreenshot = NoScreenshot.instance;
  final TextEditingController _messageController = TextEditingController();
  ScrollController historyScrollController = ScrollController();

  String? maxattendance;
  String? minleave;
  double? longitude;
  double? latitude;
  int? distance;
  int notificationcount = 0;

  List event = [];
  List news = [];
  List slider = [];
  List holiday = [];
  Map absenceTrack = {};

  bool sliderLoaded = false;
  bool newsLoaded = false;
  bool eventLoaded = false;

  int _currentSlider = 0;
  int _currentNavbar = 0;
  String? _absenceAttendanceDate;
  String? _absenceAttendanceHomeDate;

  DateTime _focusedDay = DateTime.now();

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('HH:mm:ss').format(dateTime);
  }

  // format date like 25 Maret 2024
  String formatDate(DateTime date) {
    // format month to indonesia
    final List<String> monthNames = [
      '',
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember'
    ];

    return '${date.day} ${monthNames[date.month]} ${date.year}';
  }

  void _getTime() {
    final DateTime now = DateTime.now();
    final String formattedDateTime = _formatDateTime(now);

    setState(() {
      _timeString = formattedDateTime;
    });
  }

  String greeting() {
    var hour = DateTime.now().hour;
    if (hour >= 0 && hour < 12) {
      return 'Selamat Pagi';
    } else if (hour >= 12 && hour < 15) {
      return 'Selamat Siang';
    } else if (hour >= 15 && hour < 18) {
      return 'Selamat Sore';
    } else {
      return 'Selamat Malam';
    }
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  void initState() {
    _timeString = _formatDateTime(DateTime.now());

    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _getTime();
    });

    Future.delayed(Duration.zero, () async {
      sharedPreferences = await SharedPreferences.getInstance();
      getSession();
      getAbsenceStatus();
      getEvent();
      getNews();
      getMonthlyAbsence();
      getHoliday();

      if (!kIsWeb) {
        String? token = await FirebaseMessaging.instance.getToken();

        await http.post(Uri.parse(API.FIREBASETOKEN),
            headers: {'Accept': 'application/json'},
            body: {
              'userid': sharedPreferences!.getString('id').toString(),
              'token': token
            },
            encoding: Encoding.getByName('utf-8'));
      }

      if (sharedPreferences!.getString('districtid') != null) {
        discussionStream = FirebaseFirestore.instance
            .collection('forum')
            .where('districtid',
                isEqualTo: int.parse(
                    sharedPreferences!.getString('districtid').toString()))
            .where('subdistrictid',
                isEqualTo: int.parse(
                    sharedPreferences!.getString('subdistrictid').toString()))
            .orderBy('createddate', descending: false)
            .snapshots();
      }
    });

    super.initState();
  }

  getHoliday() async {
    final response = await http.get(Uri.parse(API.ABSENCEHOLIDAY), headers: {
      'Accept': 'application/json',
    });

    var json = jsonDecode(response.body);

    // {"status":true,"data":[{"id":"29","date":"2024-01-01","description":"Tahun Baru 2024 Masehi","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"30","date":"2024-02-08","description":"Isra' Mi'raj Nabi Muhammad SAW","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"31","date":"2024-02-09","description":"Cuti Bersama Imlek 2575 Kongzili","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"32","date":"2024-02-10","description":"Tahun Baru Imlek 2575 Kongzili","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"33","date":"2024-02-14","description":"Pemilihan Umum (Pemilu)","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"34","date":"2024-03-11","description":"Hari Raya Nyepi Tahun Baru Saka 1946","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"35","date":"2024-03-12","description":"Cuti Bersama Hari Raya Nyepi","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"36","date":"2024-03-29","description":"Wafat Isa Al Masih","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"37","date":"2024-03-31","description":"Hari Paskah","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"38","date":"2024-04-08","description":"Cuti Bersama Hari Raya Idul Fitri","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"39","date":"2024-04-09","description":"Cuti Bersama Hari Raya Idul Fitri","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"40","date":"2024-04-10","description":"Hari Raya Idul Fitri 1445 H","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"41","date":"2024-04-11","description":"Hari Raya Idul Fitri 1445 H","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"42","date":"2024-04-12","description":"Cuti Bersama Hari Raya Idul Fitri","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"43","date":"2024-04-15","description":"Cuti Bersama Hari Raya Idul Fitri","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"44","date":"2024-05-01","description":"Hari Buruh Internasional","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"45","date":"2024-05-09","description":"Kenaikan Isa Al Masih","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"46","date":"2024-05-10","description":"Cuti Bersama Kenaikan Isa Al Masih","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"47","date":"2024-05-23","description":"Hari Raya Waisak 2568 BE","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"48","date":"2024-05-24","description":"Cuti Bersama Hari Raya Waisak","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"49","date":"2024-06-01","description":"Hari Lahir Pancasila","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"50","date":"2024-06-17","description":"Hari Raya Idul Adha 1445 H","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"51","date":"2024-06-18","description":"Cuti Bersama Hari Raya Idul Adha","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"52","date":"2024-07-07","description":"Tahun Baru Islam 1446 H","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"53","date":"2024-08-17","description":"Hari Kemerdekaan Republik Indonesia ke 79","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"54","date":"2024-09-16","description":"Maulid Nabi Muhammad SAW","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"55","date":"2024-12-25","description":"Hari Raya Natal","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null},{"id":"56","date":"2024-12-26","description":"Cuti Bersama Hari Raya Natal","createddate":"2024-10-22 15:57:29","createdby":null,"updateddate":null,"updatedby":null}]}
    if (json['status']) {
      List data = json['data'];

      // get only date from data
      for (var i = 0; i < data.length; i++) {
        holiday.add(data[i]['date']);
      }

      setState(() {});
    }
  }

  getMonthlyAbsence() async {
    DateTime lastDayCurrentMonth =
        DateTime.utc(_focusedDay.year, _focusedDay.month + 1)
            .subtract(const Duration(days: 1));

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(Uri.parse(API.ABSENCEHISTORY),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': sharedPreferences!.getString('id').toString(),
          'startdate': DateFormat('yyyy-MM-01').format(_focusedDay),
          'enddate': DateFormat('yyyy-MM-d').format(lastDayCurrentMonth),
        },
        encoding: Encoding.getByName('utf-8'));

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      for (var i = 0; i < json['data'].length; i++) {
        var data = json['data'][i];
        String formattedDate = DateFormat('yyyy-MM-dd')
            .format(DateTime.parse(data['createddate']));

        absenceTrack[formattedDate] = [
          AbsenceTrack(data['absencestatus']),
        ];
      }

      setState(() {});
    }
  }

  getSession() async {
    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(Uri.parse(API.SESSION),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': sharedPreferences!.getString('id').toString(),
          'token': sharedPreferences!.getString('token').toString(),
        },
        encoding: Encoding.getByName('utf-8'));

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status'] == false) {
      sharedPreferences!.clear();
      Navigator.of(context).pushReplacement(MaterialPageRoute(
        builder: (context) => const MyHomePage(),
      ));
    } else {
      setState(() {
        notificationcount = json['data']['notificationcount'];
      });
    }
  }

  getNews() async {
    try {
      setState(() {
        news = [];
        slider = [];

        sliderLoaded = false;
        newsLoaded = false;
      });

      String id = sharedPreferences!.getString('id').toString();

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.NEWSGET),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': id,
          'limit': '6',
        },
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        setState(() {
          news = json['data'];
          slider = json['data'];

          sliderLoaded = true;
          newsLoaded = true;
        });
      }
    } on HandshakeException catch (e) {
      getNews();
    }
  }

  getEvent() async {
    try {
      setState(() {
        event = [];

        eventLoaded = false;
      });

      String id = sharedPreferences!.getString('id').toString();

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.EVENTGET),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': id,
          'monthyear': DateFormat('yyyy-MM-dd').format(_focusedDay),
        },
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        setState(() {
          event = json['data'];

          eventLoaded = true;
        });
      }
    } on HandshakeException catch (e) {
      getEvent();
    }
  }

  calculateDistanceInMeters(lat1, lon1, lat2, lon2) {
    var p = 0.017453292519943295;
    var c = cos;
    var a = 0.5 -
        c((lat2 - lat1) * p) / 2 +
        c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p)) / 2;

    return 12742 * asin(sqrt(a)) * 1000;
  }

  getAbsenceStatus() async {
    try {
      String id = sharedPreferences!.getString('id').toString();

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.ABSENCESTATUS),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': id,
        },
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        String maxattendance = json['data']['maxattendance'].toString();
        String minleave = json['data']['minleave'].toString();
        String absencetype = json['data']['absencetype'].toString();
        String createddate = json['data']['createddate'].toString();
        String updateddate = json['data']['updateddate'].toString();
        String longitude = json['data']['longitude'].toString();
        String latitude = json['data']['latitude'].toString();

        if (absencetype == 'Attendance') {
          setState(() {
            _absenceAttendanceDate = createddate;
            this.maxattendance = maxattendance;
            this.minleave = minleave;
            this.longitude = double.parse(longitude);
            this.latitude = double.parse(latitude);
            this.longitude = double.parse(longitude);
            this.latitude = double.parse(latitude);
          });
        } else if (absencetype == 'Leave') {
          setState(() {
            _absenceAttendanceDate = createddate;
            _absenceAttendanceHomeDate = updateddate;
            this.maxattendance = maxattendance;
            this.minleave = minleave;
            this.longitude = double.parse(longitude);
            this.latitude = double.parse(latitude);
          });
        } else {
          setState(() {
            this.maxattendance = maxattendance;
            this.minleave = minleave;
            this.longitude = double.parse(longitude);
            this.latitude = double.parse(latitude);
          });
        }
      }

      bool serviceEnabled;
      LocationPermission permission;

      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        showAlertFailed(context, 'Please enable location service');
        return;
      }

      permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();

        if (permission == LocationPermission.denied) {
          showAlertFailed(context, 'Please enable location permission');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        showAlertFailed(context, 'Please enable location permission');
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
      );

      double distance = calculateDistanceInMeters(
          latitude, longitude, position.latitude, position.longitude);

      if (mounted) {
        setState(() {
          this.distance = distance.toInt();
        });
      }
    } on HandshakeException catch (e) {
      getAbsenceStatus();
    }
  }

  modalChangePassword() {
    TextEditingController passwordController = TextEditingController();
    TextEditingController newPasswordController = TextEditingController();
    TextEditingController confirmPasswordController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 25),
          margin: MediaQuery.of(context).viewInsets,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          height: 400,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Ubah Password',
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 18,
                      ),
                    ),
                    InkWell(
                      child: ClipOval(
                        child: Container(
                          padding: const EdgeInsets.all(5),
                          decoration: const BoxDecoration(
                            color: Color(0xFFEDEDED),
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 20,
                            color: Color(0xFF707070),
                          ),
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                    )
                  ],
                ),
              ),
              const SizedBox(height: 10),
              const Divider(
                thickness: 0.5,
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                ),
                child: Column(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: TextField(
                        obscureText: true,
                        controller: passwordController,
                        textAlignVertical: TextAlignVertical.top,
                        maxLines: 1,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 10,
                          ),
                          hintText: 'Masukkan Password Lama',
                          hintStyle: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF707070),
                            fontWeight: FontWeight.w500,
                          ),
                          border: InputBorder.none,
                          filled: true,
                          fillColor: Color(0xFFEDEDED),
                        ),
                        onChanged: (val) {},
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                ),
                child: Column(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: TextField(
                        obscureText: true,
                        controller: newPasswordController,
                        textAlignVertical: TextAlignVertical.top,
                        maxLines: 1,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 10,
                          ),
                          hintText: 'Masukkan Password Baru',
                          hintStyle: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF707070),
                            fontWeight: FontWeight.w500,
                          ),
                          border: InputBorder.none,
                          filled: true,
                          fillColor: Color(0xFFEDEDED),
                        ),
                        onChanged: (val) {},
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                ),
                child: Column(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: TextField(
                        obscureText: true,
                        controller: confirmPasswordController,
                        textAlignVertical: TextAlignVertical.top,
                        maxLines: 1,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 10,
                          ),
                          hintText: 'Masukkan Konfirmasi Password Baru',
                          hintStyle: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF707070),
                            fontWeight: FontWeight.w500,
                          ),
                          border: InputBorder.none,
                          filled: true,
                          fillColor: Color(0xFFEDEDED),
                        ),
                        onChanged: (val) {},
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                ),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: 15,
                      ),
                      backgroundColor: primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                    onPressed: () async {
                      try {
                        if (passwordController.text.isEmpty) {
                          showAlertFailed(
                              context, 'Password Lama tidak boleh kosong');
                          return;
                        }

                        if (newPasswordController.text.isEmpty) {
                          showAlertFailed(
                              context, 'Password Baru tidak boleh kosong');
                          return;
                        }

                        if (confirmPasswordController.text.isEmpty) {
                          showAlertFailed(context,
                              'Konfirmasi Password Baru tidak boleh kosong');
                          return;
                        }

                        if (newPasswordController.text !=
                            confirmPasswordController.text) {
                          showAlertFailed(context,
                              'Password Baru dan Konfirmasi Password Baru tidak sama');
                          return;
                        }

                        String id =
                            sharedPreferences!.getString('id').toString();

                        ModalDialog modalDialog = ModalDialog(context);
                        modalDialog.showLoadingDialog();

                        final response = await http.post(
                          Uri.parse(API.CHANGEPASSWORD),
                          headers: {
                            'Accept': 'application/json',
                          },
                          body: {
                            'userid': id,
                            'password': passwordController.text,
                            'newpassword': newPasswordController.text,
                            'confirmpassword': confirmPasswordController.text,
                          },
                          encoding: Encoding.getByName('utf-8'),
                        );

                        modalDialog.hideLoadingDialog();

                        var json = jsonDecode(response.body);

                        if (json['status']) {
                          Fluttertoast.showToast(
                            msg: json['message'],
                          );

                          Navigator.of(context).pop();
                          sharedPreferences!.clear();

                          Navigator.of(context)
                              .pushReplacement(MaterialPageRoute(
                            builder: (context) => const MyHomePage(),
                          ));
                        } else {
                          showAlertFailed(context, json['message']);
                        }
                      } on HandshakeException catch (e) {
                        showAlertFailed(context, e.message);
                      } catch (e) {
                        showAlertFailed(context, e.toString());
                      }
                    },
                    child: const Text(
                      'Simpan',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  modalActivityCalendar(String date, int activityCount) async {
    TextEditingController activityController = TextEditingController();

    File? pickedFile;
    XFile? pickedXFile;
    List<File> files = [];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 25),
              margin: MediaQuery.of(context).viewInsets,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                color: Colors.white,
              ),
              height: 600,
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            'Aktivitas Harian - ${formatDate(DateTime.parse(date))}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        InkWell(
                          child: ClipOval(
                            child: Container(
                              padding: const EdgeInsets.all(5),
                              decoration: const BoxDecoration(
                                color: Color(0xFFEDEDED),
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 20,
                                color: Color(0xFF707070),
                              ),
                            ),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Divider(
                    thickness: 0.5,
                  ),
                  const SizedBox(height: 10),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: TextField(
                            controller: activityController,
                            textAlignVertical: TextAlignVertical.top,
                            maxLines: 5,
                            minLines: 5,
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 15, vertical: 10),
                              hintText: 'Ketik aktivitas anda disini ...',
                              hintStyle: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF707070),
                                fontWeight: FontWeight.w500,
                              ),
                              border: InputBorder.none,
                              filled: true,
                              fillColor: Color(0xFFEDEDED),
                            ),
                            onChanged: (val) {},
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(height: 10),
                        InkWell(
                          child: Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              color: const Color(0xFFEDEDED),
                              borderRadius: BorderRadius.circular(5),
                            ),
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.upload_outlined,
                                  color: Color(0xFF999999),
                                  size: 30,
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  pickedFile == null
                                      ? 'Upload Foto Pendukung (Opsional)'
                                      : pickedFile!.path.split('/').last,
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFF999999),
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                          onTap: () async {
                            XFile? imagePicker = await ImagePicker().pickImage(
                              source: ImageSource.gallery,
                            );

                            if (imagePicker != null) {
                              setState(() {
                                pickedFile = File(imagePicker.path);
                                pickedXFile = imagePicker;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 5),
                        const Text(
                          '*Batas ukuran file: 3MB',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Text(
                          'Dokumen pendukung lainnya',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: files.map(
                                (e) {
                                  return Container(
                                    margin: const EdgeInsets.only(right: 5),
                                    child: Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: Stack(
                                        children: [
                                          Center(
                                            child: Icon(
                                              e.path
                                                          .split('/')
                                                          .last
                                                          .split('.')
                                                          .last ==
                                                      'pdf'
                                                  ? Icons.picture_as_pdf
                                                  : Icons.image,
                                              color: Colors.grey,
                                            ),
                                          ),
                                          Positioned(
                                            right: 0,
                                            top: 0,
                                            child: InkWell(
                                              child: Container(
                                                width: 15,
                                                height: 15,
                                                decoration: BoxDecoration(
                                                  color: Colors.red,
                                                ),
                                                child: Center(
                                                  child: Icon(
                                                    Icons.close,
                                                    color: Colors.white,
                                                    size: 10,
                                                  ),
                                                ),
                                              ),
                                              onTap: () {
                                                setState(() {
                                                  files.remove(e);
                                                });
                                              },
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ).toList() +
                              [
                                Container(
                                  child: InkWell(
                                    child: Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: Center(
                                        child:
                                            Icon(Icons.add, color: Colors.grey),
                                      ),
                                    ),
                                    onTap: () async {
                                      FilePickerResult? result =
                                          await FilePicker.platform.pickFiles(
                                        type: FileType.custom,
                                        allowedExtensions: [
                                          'pdf',
                                          'doc',
                                          'docx',
                                          'jpg',
                                          'jpeg',
                                          'png'
                                        ],
                                      );

                                      if (result != null) {
                                        File file =
                                            File(result.files.single.path!);

                                        setState(() {
                                          files.add(file);
                                        });
                                      }
                                    },
                                  ),
                                )
                              ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: 15),
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                vertical: 15,
                              ),
                              backgroundColor: primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                            onPressed: () async {
                              if (activityController.text.isEmpty) {
                                showAlertFailed(
                                    context, 'Aktivitas tidak boleh kosong');
                                return;
                              }

                              ModalDialog modalDialog = ModalDialog(context);
                              modalDialog.showLoadingDialog();

                              bool serviceEnabled;
                              LocationPermission permission;

                              serviceEnabled =
                                  await Geolocator.isLocationServiceEnabled();
                              if (!serviceEnabled) {
                                modalDialog.hideLoadingDialog();
                                showAlertFailed(
                                    context, 'Please enable location service');
                                return;
                              }

                              permission = await Geolocator.checkPermission();
                              if (permission == LocationPermission.denied) {
                                permission =
                                    await Geolocator.requestPermission();

                                if (permission == LocationPermission.denied) {
                                  modalDialog.hideLoadingDialog();
                                  showAlertFailed(context,
                                      'Please enable location permission');
                                  return;
                                }
                              }

                              if (permission ==
                                  LocationPermission.deniedForever) {
                                modalDialog.hideLoadingDialog();
                                showAlertFailed(context,
                                    'Please enable location permission');
                                return;
                              }

                              if (!kIsWeb) {
                                bool isFakeLocation = await DetectFakeLocation()
                                    .detectFakeLocation();

                                if (isFakeLocation) {
                                  modalDialog.hideLoadingDialog();
                                  showAlertFailed(
                                      context, 'Fake location detected');
                                  return;
                                }
                              }

                              Position position =
                                  await Geolocator.getCurrentPosition(
                                desiredAccuracy: LocationAccuracy.best,
                              );

                              if (position.isMocked) {
                                modalDialog.hideLoadingDialog();
                                showAlertFailed(
                                    context, 'Fake location detected');
                                return;
                              }

                              final request = http.MultipartRequest(
                                  'POST', Uri.parse(API.DAILYACTIVITYADD));

                              if (pickedFile != null || pickedXFile != null) {
                                if (!kIsWeb) {
                                  request.files.add(
                                      await http.MultipartFile.fromPath(
                                          'document', pickedFile!.path));
                                } else {
                                  request.files.add(
                                      http.MultipartFile.fromBytes('document',
                                          await pickedXFile!.readAsBytes(),
                                          filename: pickedXFile!.name));
                                }
                              }

                              for (var file in files) {
                                if (!kIsWeb) {
                                  request.files.add(
                                    await http.MultipartFile.fromPath(
                                      'other_document[]',
                                      file.path,
                                    ),
                                  );
                                } else {
                                  request.files.add(
                                    http.MultipartFile.fromBytes(
                                      'other_document[]',
                                      await file.readAsBytes(),
                                      filename: file.path.split('/').last,
                                    ),
                                  );
                                }
                              }

                              request.fields['userappid'] =
                                  sharedPreferences!.getString('id').toString();
                              request.fields['activity'] =
                                  activityController.text;
                              request.fields['date'] = DateFormat('yyyy-MM-dd')
                                  .format(DateTime.parse(date));
                              request.fields['latitude'] =
                                  position.latitude.toString();
                              request.fields['longitude'] =
                                  position.longitude.toString();

                              final response = await request.send();
                              final responseData =
                                  await response.stream.bytesToString();

                              modalDialog.hideLoadingDialog();

                              var json = jsonDecode(responseData);

                              if (json['status']) {
                                Fluttertoast.showToast(msg: json['message']);

                                Navigator.of(context).pop();
                              } else {
                                showAlertFailed(context, json['message']);
                              }
                            },
                            child: const Text(
                              'Posting',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(10),
                          margin: const EdgeInsets.only(top: 10),
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEDEDED).withOpacity(.8),
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Icons.history_outlined,
                                    color: Color(0xFF707070),
                                    size: 20,
                                  ),
                                  const SizedBox(width: 5),
                                  Text(
                                    'Aktivitas hari ini: $activityCount',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFF707070),
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                              InkWell(
                                child: const Text(
                                  'Lihat Aktivitas',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: primaryColor,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                onTap: () {
                                  Navigator.of(context).pop();
                                  Navigator.of(context).push(MaterialPageRoute(
                                    builder: (context) => MyHistoryActivity(
                                      date: DateFormat('yyyy-MM-dd')
                                          .format(DateTime.parse(date)),
                                    ),
                                  ));
                                },
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }

  modalChangeProfile() async {
    TextEditingController nameController = TextEditingController(
        text: sharedPreferences!.getString('name').toString());
    TextEditingController emailController = TextEditingController(
        text: sharedPreferences!.getString('email').toString());

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 25),
              margin: MediaQuery.of(context).viewInsets,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              height: 350,
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Ubah Profil',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 18,
                          ),
                        ),
                        InkWell(
                          child: ClipOval(
                            child: Container(
                              padding: const EdgeInsets.all(5),
                              decoration: const BoxDecoration(
                                color: Color(0xFFEDEDED),
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 20,
                                color: Color(0xFF707070),
                              ),
                            ),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Divider(
                    thickness: 0.5,
                  ),
                  const SizedBox(height: 10),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                    ),
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: TextField(
                            controller: nameController,
                            textAlignVertical: TextAlignVertical.top,
                            maxLines: 1,
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 10,
                              ),
                              hintText: 'Masukkan Nama',
                              hintStyle: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF707070),
                                fontWeight: FontWeight.w500,
                              ),
                              border: InputBorder.none,
                              filled: true,
                              fillColor: Color(0xFFEDEDED),
                            ),
                            onChanged: (val) {},
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                    ),
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: TextField(
                            controller: emailController,
                            textAlignVertical: TextAlignVertical.top,
                            maxLines: 1,
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 10,
                              ),
                              hintText: 'Masukkan Alamat Email',
                              hintStyle: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF707070),
                                fontWeight: FontWeight.w500,
                              ),
                              border: InputBorder.none,
                              filled: true,
                              fillColor: Color(0xFFEDEDED),
                            ),
                            onChanged: (val) {},
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: 20),
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                vertical: 15,
                              ),
                              backgroundColor: primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                            onPressed: () async {
                              try {
                                String id = sharedPreferences!
                                    .getString('id')
                                    .toString();

                                ModalDialog modalDialog = ModalDialog(context);
                                modalDialog.showLoadingDialog();

                                final response = await http.post(
                                  Uri.parse(API.CHANGEPROFILE),
                                  headers: {
                                    'Accept': 'application/json',
                                  },
                                  body: {
                                    'userid': id,
                                    'name': nameController.text,
                                    'email': emailController.text,
                                  },
                                  encoding: Encoding.getByName('utf-8'),
                                );

                                modalDialog.hideLoadingDialog();

                                var json = jsonDecode(response.body);

                                if (json['status']) {
                                  Fluttertoast.showToast(msg: json['message']);
                                  Navigator.of(context).pop();

                                  sharedPreferences!.setString(
                                    'name',
                                    nameController.text,
                                  );

                                  sharedPreferences!.setString(
                                    'email',
                                    emailController.text,
                                  );
                                } else {
                                  showAlertFailed(context, json['message']);
                                }
                              } on HandshakeException catch (e) {
                                showAlertFailed(context, e.message);
                              } catch (e) {
                                showAlertFailed(context, e.toString());
                              }
                            },
                            child: const Text(
                              'Simpan',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            );
          },
        );
      },
    );
  }

  String calculateTimeDifference(DateTime startDate, DateTime endDate) {
    Duration difference = startDate.difference(endDate).abs();

    // Menghitung jam, menit, dan detik
    int hours = difference.inHours;
    int minutes = (difference.inMinutes - hours * 60);
    int seconds = (difference.inSeconds - hours * 3600 - minutes * 60);

    // Format hasil ke HH:mm:ss
    String formattedDifference =
        '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

    return formattedDifference;
  }

  String compareDateTime(DateTime createdDate, DateTime updatedDate) {
    Duration difference = updatedDate.difference(createdDate);
    int hours = difference.inHours;
    int minutes = difference.inMinutes.remainder(60);
    int seconds = difference.inSeconds.remainder(60);

    String formattedDifference = DateFormat('HH:mm:ss')
        .format(DateTime(0, 0, 0, hours, minutes, seconds));
    return formattedDifference;
  }

  String formatDateTodayInIndonesian({date}) {
    // Define Indonesian day names and month names
    final List<String> dayNames = [
      'Senin',
      'Selasa',
      'Rabu',
      'Kamis',
      'Jumat',
      'Sabtu',
      'Minggu',
    ];
    final List<String> monthNames = [
      '',
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember'
    ];

    // Get current date
    DateTime? now;
    if (date != null) {
      now = date;
    } else {
      now = DateTime.now();
    }

    // Format the date
    String dayName = dayNames[now!.weekday - 1];
    String day = DateFormat.d().format(now);
    String month = monthNames[now.month];
    String year = DateFormat.y().format(now);

    // Construct the formatted date string
    String formattedDate = '$dayName, $day $month $year';

    return formattedDate;
  }

  Stream<QuerySnapshot>? discussionStream;
  RefreshController dashboardRefreshController =
      RefreshController(initialRefresh: false);
  RefreshController eventRefreshController =
      RefreshController(initialRefresh: false);

  bool initialDiscussion = false;

  List<Widget> _getContent() {
    return [
      SmartRefresher(
        scrollDirection: Axis.vertical,
        controller: dashboardRefreshController,
        onRefresh: () async {
          await getAbsenceStatus();
          await getNews();
          dashboardRefreshController.refreshCompleted();
        },
        child: Container(
          color: Colors.white,
          child: SingleChildScrollView(
            padding:
                const EdgeInsets.symmetric(horizontal: 0).copyWith(bottom: 25),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            primaryColor,
                            Color(0xFF005d71),
                          ],
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: SizedBox(
                              height: 200,
                              child: WaveWidget(
                                config: CustomConfig(
                                  colors: [
                                    const Color(0xFF009b6e).withOpacity(.1),
                                    const Color(0xFF009b6e).withOpacity(.2),
                                    const Color(0xFF009b6e).withOpacity(.3),
                                    const Color(0xFF009b6e).withOpacity(.4),
                                  ],
                                  durations: [6000, 10800, 19440, 35000],
                                  heightPercentages: [0.25, 0.26, 0.28, 0.31],
                                ),
                                size: const Size(
                                    double.infinity, double.infinity),
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 50,
                              horizontal: 20,
                            ).copyWith(bottom: 25),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Row(
                                        children: [
                                          ClipOval(
                                            child: SizedBox(
                                              width: 50,
                                              height: 50,
                                              child: sharedPreferences ==
                                                          null ||
                                                      sharedPreferences!.getString(
                                                              'profileimage') ==
                                                          null ||
                                                      sharedPreferences!
                                                              .getString(
                                                                  'profileimage')
                                                              .toString() ==
                                                          ''
                                                  ? Image.asset(
                                                      'images/profile.png',
                                                      width: 50,
                                                      height: 50,
                                                      fit: BoxFit.cover,
                                                    )
                                                  : Image.network(
                                                      '${API.GOOGLEAPIS}/${sharedPreferences!.getString('profileimage')}',
                                                      width: 50,
                                                      height: 50,
                                                      fit: BoxFit.cover,
                                                    ),
                                            ),
                                          ),
                                          const SizedBox(width: 10),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  '${greeting()},',
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w400,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                                Text(
                                                  (sharedPreferences != null
                                                      ? sharedPreferences!
                                                          .getString('name')
                                                          .toString()
                                                      : 'Guest'),
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w700,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                                // Text(
                                                //   'NIP. ${sharedPreferences != null ? sharedPreferences!.getString('nip').toString() : '-'}',
                                                //   style: const TextStyle(
                                                //     fontSize: 10,
                                                //     color: Colors.white,
                                                //   ),
                                                // ),
                                                Text(
                                                  sharedPreferences != null
                                                      ? sharedPreferences!
                                                          .getString('position')
                                                          .toString()
                                                      : '-',
                                                  style: const TextStyle(
                                                    fontSize: 10,
                                                    color: Colors.white,
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    InkWell(
                                      child: Stack(children: [
                                        const Icon(
                                          Icons.notifications_outlined,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                        Positioned(
                                          right: 0,
                                          top: 0,
                                          child: Container(
                                            width: 15,
                                            height: 15,
                                            decoration: const BoxDecoration(
                                              color: Colors.red,
                                              shape: BoxShape.circle,
                                            ),
                                            child: Center(
                                              child: Text(
                                                notificationcount < 99
                                                    ? notificationcount
                                                        .toString()
                                                    : '99+',
                                                style: const TextStyle(
                                                  fontSize: 10,
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                              ),
                                            ),
                                          ),
                                        )
                                      ]),
                                      onTap: () async {
                                        await Navigator.of(context)
                                            .push(MaterialPageRoute(
                                          builder: (context) =>
                                              MyNotification(),
                                        ))
                                            .then(
                                          (value) {
                                            if (value != null &&
                                                value['type'] != null) {
                                              if (value['type'] == 'event') {
                                                setState(() {
                                                  _persistentTabController
                                                      .jumpToTab(1);
                                                  _currentNavbar = 1;
                                                });
                                              } else if (value['type'] ==
                                                  'news') {
                                                Navigator.of(context)
                                                    .push(MaterialPageRoute(
                                                  builder: (context) =>
                                                      MyNewsAll(),
                                                ));
                                              }
                                            }
                                          },
                                        );
                                      },
                                    )
                                  ],
                                ),
                                const SizedBox(height: 25),
                                Container(
                                  margin:
                                      const EdgeInsets.only(top: 5, bottom: 5),
                                  width: MediaQuery.of(context).size.width,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        _timeString!,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 24,
                                          color: Colors.white,
                                        ),
                                      ),
                                      Text(
                                        formatDateTodayInIndonesian(),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 14,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Text(
                                  'Anda berada pada jarak ${distance ?? 0}m dari kantor',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 10,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 35),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        children: [
                                          ClipOval(
                                            child: Container(
                                              width: 40,
                                              height: 40,
                                              decoration: const BoxDecoration(
                                                color: Color(0xFFEDEDED),
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons
                                                        .arrow_downward_outlined,
                                                    color: primaryColor,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 5),
                                          Text(
                                            _absenceAttendanceDate != null
                                                ? DateFormat('HH:mm:ss').format(
                                                    DateTime.parse(
                                                        _absenceAttendanceDate
                                                            .toString()))
                                                : '--:--:--',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              color: Colors.white,
                                            ),
                                          ),
                                          const Text(
                                            'Presensi Masuk',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Column(
                                        children: [
                                          ClipOval(
                                            child: Container(
                                              width: 40,
                                              height: 40,
                                              decoration: const BoxDecoration(
                                                color: Color(0xFFEDEDED),
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.arrow_upward_outlined,
                                                    color: primaryColor,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 5),
                                          Text(
                                            _absenceAttendanceHomeDate != null
                                                ? DateFormat('HH:mm:ss').format(
                                                    DateTime.parse(
                                                        _absenceAttendanceHomeDate
                                                            .toString()))
                                                : '--:--:--',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              color: Colors.white,
                                            ),
                                          ),
                                          const Text(
                                            'Presensi Pulang',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          ClipOval(
                                            child: Container(
                                              width: 40,
                                              height: 40,
                                              decoration: const BoxDecoration(
                                                color: Color(0xFFEDEDED),
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.alarm_outlined,
                                                    color: primaryColor,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 5),
                                          Text(
                                            _absenceAttendanceDate != null &&
                                                    _absenceAttendanceHomeDate !=
                                                        null
                                                ? calculateTimeDifference(
                                                    DateTime.parse(
                                                        _absenceAttendanceDate
                                                            .toString()),
                                                    DateTime.parse(
                                                        _absenceAttendanceHomeDate
                                                            .toString()))
                                                : '--:--:--',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              color: Colors.white,
                                            ),
                                          ),
                                          const Text(
                                            'Jam Kerja',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(height: 25),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        const Text(
                                          'Jadwal Masuk',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          maxattendance != null
                                              ? maxattendance!
                                              : '-',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        const Text(
                                          'Jadwal Pulang',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          minleave != null ? minleave! : '-',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                                const SizedBox(height: 25),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: InkWell(
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: BoxDecoration(
                                                color: const Color(0xFF0092ff),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.1),
                                                    blurRadius: 5,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.fingerprint_outlined,
                                                    color: Colors.white,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(height: 5),
                                            const Text(
                                              'Presensi',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                        onTap: () async {
                                          await Navigator.of(context)
                                              .push(MaterialPageRoute(
                                            builder: (context) => MyAbsence(
                                              sharedPreferences:
                                                  sharedPreferences!,
                                            ),
                                          ))
                                              .then((value) async {
                                            await getAbsenceStatus();
                                          });
                                        },
                                      ),
                                    ),
                                    Expanded(
                                      child: InkWell(
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: BoxDecoration(
                                                color: const Color(0xFFe67800),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.1),
                                                    blurRadius: 5,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons
                                                        .manage_history_outlined,
                                                    color: Colors.white,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(height: 5),
                                            const Text(
                                              'Aktivitas',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                        onTap: () async {
                                          ModalDialog modal =
                                              ModalDialog(context);

                                          modal.showLoadingDialog();

                                          final response = await http.post(
                                            Uri.parse(
                                                API.DAILYACTIVITYGETTODAY),
                                            headers: {
                                              'Accept': 'application/json',
                                            },
                                            body: {
                                              'userappid': sharedPreferences!
                                                  .getString('id')
                                                  .toString(),
                                            },
                                            encoding:
                                                Encoding.getByName('UTF-8'),
                                          );

                                          modal.hideLoadingDialog();

                                          var json = jsonDecode(response.body);

                                          if (json['status']) {
                                            Navigator.of(context)
                                                .push(MaterialPageRoute(
                                              builder: (context) =>
                                                  MyAddActivity(
                                                activityCount:
                                                    json['todayactivity'],
                                              ),
                                            ));
                                          } else {
                                            Navigator.of(context)
                                                .push(MaterialPageRoute(
                                              builder: (context) =>
                                                  MyAddActivity(
                                                activityCount: 0,
                                              ),
                                            ));
                                          }
                                        },
                                      ),
                                    ),
                                    Expanded(
                                      child: InkWell(
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: BoxDecoration(
                                                color: const Color(0xFFff4c58),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.1),
                                                    blurRadius: 5,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.access_time_outlined,
                                                    color: Colors.white,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(height: 5),
                                            const Text(
                                              'Pengajuan Izin',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                color: Colors.white,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                        onTap: () async {
                                          Navigator.of(context)
                                              .push(MaterialPageRoute(
                                            builder: (context) => MyCuti(),
                                          ));
                                        },
                                      ),
                                    ),
                                    Expanded(
                                      child: InkWell(
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: BoxDecoration(
                                                color: const Color(0xFF2f4858),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.1),
                                                    blurRadius: 5,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.qr_code_rounded,
                                                    color: Colors.white,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(height: 5),
                                            const Text(
                                              'Daftar Hadir',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                        onTap: () async {
                                          Navigator.of(context)
                                              .push(MaterialPageRoute(
                                            builder: (context) =>
                                                MyListOfAttendance(),
                                          ));
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 20),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: InkWell(
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 50,
                                              height: 50,
                                              decoration: BoxDecoration(
                                                color: const Color(0xFFcace60),
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.1),
                                                    blurRadius: 5,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: const Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.list_outlined,
                                                    color: Colors.white,
                                                    size: 20,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(height: 5),
                                            const Text(
                                              'Penugasan',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                        onTap: () async {
                                          Navigator.of(context)
                                              .push(MaterialPageRoute(
                                            builder: (context) => MyTask(),
                                          ));
                                        },
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                  ),
                  child: const Text(
                    'Kalender Aktivitas',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                TableCalendar(
                  availableGestures: AvailableGestures.none,
                  focusedDay: _focusedDay,
                  firstDay: DateTime.utc(1970),
                  lastDay: DateTime.utc(2050),
                  headerStyle: const HeaderStyle(
                    formatButtonVisible: false,
                  ),
                  calendarStyle: const CalendarStyle(
                    selectedDecoration: BoxDecoration(
                      color: primaryColor,
                      shape: BoxShape.circle,
                    ),
                    todayDecoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                    selectedTextStyle: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                    todayTextStyle: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  onPageChanged: (focusedDay) {
                    _focusedDay = focusedDay;
                    getEvent();
                    getMonthlyAbsence();
                  },
                  onDaySelected: (selectedDay, focusedDay) async {
                    if (selectedDay.isAfter(DateTime.now())) {
                      return;
                    }

                    String date = DateFormat('yyyy-MM-dd').format(selectedDay);

                    ModalDialog modal = ModalDialog(context);

                    modal.showLoadingDialog();

                    final response = await http.post(
                      Uri.parse(API.DAILYACTIVITYGETTODAY),
                      headers: {
                        'Accept': 'application/json',
                      },
                      body: {
                        'userappid':
                            sharedPreferences!.getString('id').toString(),
                        'date': date,
                      },
                      encoding: Encoding.getByName('utf-8'),
                    );

                    modal.hideLoadingDialog();

                    var json = jsonDecode(response.body);

                    if (json['status']) {
                      modalActivityCalendar(
                          selectedDay.toString(), json['todayactivity']);
                    } else {
                      modalActivityCalendar(selectedDay.toString(), 0);
                    }
                  },
                  calendarBuilders: CalendarBuilders(
                    markerBuilder: (context, day, events) {
                      if (events.isNotEmpty) {
                        return Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            margin: const EdgeInsets.only(bottom: 1),
                            width: 5,
                            height: 5,
                            decoration: BoxDecoration(
                              color: events.first.toString() == 'Late'
                                  ? Colors.red
                                  : primaryColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                        );
                      }
                    },
                    holidayBuilder: (context, day, events) {
                      return Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 1),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              day.day.toString(),
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  eventLoader: (day) {
                    String formatter = DateFormat('yyyy-MM-dd').format(day);

                    if (absenceTrack.containsKey(formatter)) {
                      return absenceTrack[formatter];
                    }

                    return [];
                  },
                  holidayPredicate: (day) {
                    return holiday.contains(DateFormat('y-MM-dd').format(day));
                  },
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15,
                  ),
                  child: Column(
                    children: [
                      sliderLoaded
                          ? (slider.isNotEmpty
                              ? Column(
                                  children: [
                                    CarouselSlider(
                                      carouselController: _sliderController,
                                      options: CarouselOptions(
                                        height: 175,
                                        viewportFraction: 1,
                                        autoPlay: true,
                                        autoPlayAnimationDuration:
                                            const Duration(milliseconds: 500),
                                        onPageChanged: (index, reason) {
                                          setState(() {
                                            _currentSlider = index;
                                          });
                                        },
                                      ),
                                      items: slider.map((e) {
                                        return Builder(
                                          builder: (BuildContext context) {
                                            return InkWell(
                                              child: SizedBox(
                                                width: MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      top: 0,
                                                      left: 0,
                                                      right: 0,
                                                      bottom: 0,
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                        child: Image.network(
                                                          '${API.GOOGLEAPIS}/${e['thumbnail']}',
                                                          fit: BoxFit.cover,
                                                        ),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      top: 0,
                                                      left: 0,
                                                      right: 0,
                                                      bottom: 0,
                                                      child: Container(
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.black
                                                              .withOpacity(0.5),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(10),
                                                        ),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      bottom: 20,
                                                      left: 20,
                                                      right: 20,
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            e['categoryname'],
                                                            style:
                                                                const TextStyle(
                                                              fontSize: 12,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400,
                                                              color:
                                                                  Colors.white,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            height: 3,
                                                          ),
                                                          Text(
                                                            e['name'],
                                                            style:
                                                                const TextStyle(
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w700,
                                                              color:
                                                                  Colors.white,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              onTap: () {
                                                Navigator.of(context)
                                                    .push(MaterialPageRoute(
                                                  builder: (context) =>
                                                      MyNews(data: e),
                                                ));
                                              },
                                            );
                                          },
                                        );
                                      }).toList(),
                                    ),
                                    const SizedBox(height: 10),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: news.asMap().entries.map((i) {
                                        return GestureDetector(
                                          onTap: () => _sliderController!
                                              .animateToPage(i.key),
                                          child: Container(
                                            width: 8,
                                            height: 8,
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 2),
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: _currentSlider == i.key
                                                  ? primaryColor
                                                  : const Color(0xFFD9D9D9),
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                    ),
                                  ],
                                )
                              : Container())
                          : SizedBox(
                              height: 175,
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                            ),
                      const SizedBox(
                        height: 30,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            'Informasi Terbaru',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          InkWell(
                            child: const Text(
                              'Selengkapnya',
                              style: TextStyle(
                                fontSize: 14,
                                color: primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            onTap: () {
                              Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) => const MyNewsAll(),
                              ));
                            },
                          )
                        ],
                      ),
                      const SizedBox(height: 15),
                      newsLoaded
                          ? (news.isNotEmpty
                              ? Column(
                                  children: news.map((e) {
                                    return InkWell(
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              Container(
                                                width: 100,
                                                height: 100,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  image: DecorationImage(
                                                    image: NetworkImage(
                                                        '${API.GOOGLEAPIS}/${e['thumbnail']}'),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 10),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      e['name'],
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w700,
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),
                                                    Text(
                                                      DateFormat('dd MMMM yyyy')
                                                          .format(DateTime.parse(
                                                              e['createddate'])),
                                                      style: const TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          color: Color(
                                                              0xFF888888)),
                                                    ),
                                                  ],
                                                ),
                                              )
                                            ],
                                          ),
                                          const SizedBox(height: 15)
                                        ],
                                      ),
                                      onTap: () {
                                        Navigator.of(context)
                                            .push(MaterialPageRoute(
                                          builder: (context) => MyNews(data: e),
                                        ));
                                      },
                                    );
                                  }).toList(),
                                )
                              : Container(
                                  margin: const EdgeInsets.only(top: 40),
                                  width: MediaQuery.of(context).size.width,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        'images/searching.png',
                                        width: 200,
                                      ),
                                      const SizedBox(height: 20),
                                      const Text(
                                        'Tidak ada berita',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ))
                          : Column(
                              children: [1, 2, 3, 4, 5, 6].map((e) {
                                return Column(
                                  children: [
                                    Row(
                                      children: [
                                        SizedBox(
                                          width: 100,
                                          height: 100,
                                          child: Shimmer.fromColors(
                                            baseColor: Colors.grey[300]!,
                                            highlightColor: Colors.grey[100]!,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 10),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Shimmer.fromColors(
                                                baseColor: Colors.grey[300]!,
                                                highlightColor:
                                                    Colors.grey[100]!,
                                                child: Container(
                                                  width: double.infinity,
                                                  height: 18,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              Shimmer.fromColors(
                                                baseColor: Colors.grey[300]!,
                                                highlightColor:
                                                    Colors.grey[100]!,
                                                child: Container(
                                                  width: 100,
                                                  height: 15,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                    const SizedBox(height: 15)
                                  ],
                                );
                              }).toList(),
                            ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
      Container(
        color: Colors.white,
        child: SmartRefresher(
          scrollDirection: Axis.vertical,
          controller: eventRefreshController,
          onRefresh: () async {
            await getEvent();
            eventRefreshController.refreshCompleted();
          },
          child: Container(
            color: Colors.white,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TableCalendar(
                    availableGestures: AvailableGestures.none,
                    focusedDay: _focusedDay,
                    firstDay: DateTime.utc(1970),
                    lastDay: DateTime.utc(2050),
                    headerStyle: const HeaderStyle(
                      formatButtonVisible: false,
                    ),
                    calendarStyle: const CalendarStyle(
                      selectedDecoration: BoxDecoration(
                        color: primaryColor,
                        shape: BoxShape.circle,
                      ),
                      todayDecoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                      selectedTextStyle: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                      todayTextStyle: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    onPageChanged: (focusedDay) {
                      _focusedDay = focusedDay;
                      getEvent();
                    },
                    onDaySelected: (selectedDay, focusedDay) async {
                      if (selectedDay.isAfter(DateTime.now())) {
                        return;
                      }

                      String date =
                          DateFormat('yyyy-MM-dd').format(selectedDay);

                      ModalDialog modal = ModalDialog(context);

                      modal.showLoadingDialog();

                      final response = await http.post(
                        Uri.parse(API.DAILYACTIVITYGETTODAY),
                        headers: {
                          'Accept': 'application/json',
                        },
                        body: {
                          'userappid':
                              sharedPreferences!.getString('id').toString(),
                          'date': date,
                        },
                        encoding: Encoding.getByName('utf-8'),
                      );

                      modal.hideLoadingDialog();

                      var json = jsonDecode(response.body);

                      if (json['status']) {
                        modalActivityCalendar(
                            selectedDay.toString(), json['todayactivity']);
                      } else {
                        modalActivityCalendar(selectedDay.toString(), 0);
                      }
                    },
                  ),
                  const Divider(
                    thickness: .5,
                  ),
                  const SizedBox(height: 10),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Text(
                      'Jadwal Event Bulan ${DateFormat('MMMM yyyy').format(_focusedDay)}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    margin: const EdgeInsets.only(bottom: 15),
                    child: eventLoaded
                        ? event.isNotEmpty
                            ? Column(
                                children: event.map((e) {
                                  DateTime eventDate = DateTime.parse(
                                      '${e['eventdate']} ${e['eventtime']}');

                                  String? eventStatus;
                                  if (DateTime.now().isBefore(eventDate)) {
                                    eventStatus = 'Segera';
                                  } else {
                                    eventStatus = 'Selesai';
                                  }

                                  return Column(
                                    children: [
                                      const Divider(),
                                      InkWell(
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Row(
                                                children: [
                                                  Container(
                                                    width: 30,
                                                    height: 30,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                      color: primaryColor,
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        DateFormat('dd').format(
                                                            DateTime.parse(e[
                                                                    'eventdate']
                                                                .toString())),
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: 13,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: Text(
                                                      e['name'],
                                                      style: const TextStyle(
                                                        fontSize: 13,
                                                        color:
                                                            Color(0xFF555555),
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            Text(
                                              eventStatus,
                                              style: TextStyle(
                                                fontSize: 13,
                                                color: eventStatus != 'Selesai'
                                                    ? primaryColor
                                                    : const Color(0xFF0AA867),
                                              ),
                                            )
                                          ],
                                        ),
                                        onTap: () {
                                          Navigator.of(context)
                                              .push(MaterialPageRoute(
                                            builder: (context) => MyEvent(
                                              data: e,
                                            ),
                                          ));
                                        },
                                      ),
                                    ],
                                  );
                                }).toList(),
                              )
                            : Container(
                                margin: const EdgeInsets.only(top: 40),
                                width: MediaQuery.of(context).size.width,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Image.asset(
                                      'images/no-data.png',
                                      width: 100,
                                    ),
                                    const SizedBox(height: 10),
                                    const Text(
                                      'Tidak ada event',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFF555555),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                        : Column(
                            children: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((e) {
                              return Column(
                                children: [
                                  const Divider(),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Row(
                                          children: [
                                            Container(
                                              width: 30,
                                              height: 30,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                              ),
                                              child: Shimmer.fromColors(
                                                baseColor: Colors.grey[300]!,
                                                highlightColor:
                                                    Colors.grey[100]!,
                                                child: Container(
                                                  width: 30,
                                                  height: 30,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5),
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Shimmer.fromColors(
                                              baseColor: Colors.grey[300]!,
                                              highlightColor: Colors.grey[100]!,
                                              child: Container(
                                                width: 150,
                                                height: 15,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        child: Container(
                                          width: 50,
                                          height: 15,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
      Container(),
      Container(
        color: Colors.white,
        child: Stack(
          children: [
            discussionStream != null
                ? StreamBuilder(
                    stream: discussionStream,
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      } else {
                        return ListView.builder(
                          controller: historyScrollController,
                          padding: const EdgeInsets.symmetric(horizontal: 15)
                              .copyWith(bottom: 75, top: 15),
                          itemCount: snapshot.data!.docs.length,
                          itemBuilder: (context, index) {
                            if (snapshot.data!.docs[index]['userappid'] ==
                                int.parse(sharedPreferences!
                                    .getString('id')
                                    .toString())) {
                              return Container(
                                margin: const EdgeInsets.only(bottom: 20),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            snapshot.data!.docs[index]['name'],
                                            style: const TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                          const SizedBox(height: 5),
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: const BoxDecoration(
                                              color: Color(0xFFDDDDDD),
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(10),
                                                topRight: Radius.circular(10),
                                                bottomLeft: Radius.circular(10),
                                              ),
                                            ),
                                            child: Text(
                                              snapshot.data!.docs[index]
                                                  ['message'],
                                              style: const TextStyle(
                                                fontSize: 11,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          Text(
                                            DateFormat('dd MMMM yyyy, HH:mm')
                                                .format(
                                              DateTime.parse(
                                                snapshot.data!
                                                    .docs[index]['createddate']
                                                    .toDate()
                                                    .toString(),
                                              ),
                                            ),
                                            style: const TextStyle(
                                              fontSize: 8,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    SizedBox(
                                      width: 30,
                                      height: 30,
                                      child: ClipOval(
                                        child: Image.network(
                                          '${API.API_URL}/userapps/profileimage/${snapshot.data!.docs[index]['userappid']}',
                                          fit: BoxFit.cover,
                                          width: 30,
                                          height: 30,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            } else {
                              return Container(
                                margin: const EdgeInsets.only(bottom: 20),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 30,
                                      height: 30,
                                      child: ClipOval(
                                        child: snapshot.data!.docs[index]
                                                    ['role'] ==
                                                'User App'
                                            ? Image.network(
                                                '${API.API_URL}/userapps/profileimage/${snapshot.data!.docs[index]['userappid']}',
                                                fit: BoxFit.cover,
                                                width: 30,
                                                height: 30,
                                              )
                                            : Image.network(
                                                '${API.API_URL}/user/profileimage/${snapshot.data!.docs[index]['userid']}',
                                                fit: BoxFit.cover,
                                                width: 30,
                                                height: 30,
                                              ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Text(
                                                snapshot.data!.docs[index]
                                                    ['name'],
                                                style: const TextStyle(
                                                  fontSize: 10,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                              ),
                                              const SizedBox(width: 3),
                                              const Text('-',
                                                  style:
                                                      TextStyle(fontSize: 12)),
                                              const SizedBox(width: 3),
                                              Text(
                                                snapshot.data!.docs[index]
                                                            ['role'] ==
                                                        'Subdistricts'
                                                    ? 'Admin Desa'
                                                    : (snapshot.data!
                                                                    .docs[index]
                                                                ['role'] ==
                                                            'User App'
                                                        ? 'Anggota Desa'
                                                        : snapshot.data!
                                                                .docs[index]
                                                            ['role']),
                                                style: const TextStyle(
                                                  fontSize: 10,
                                                  fontWeight: FontWeight.w700,
                                                  color: primaryColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 5),
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: const BoxDecoration(
                                              color: Color(0xFFDDDDDD),
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(10),
                                                topRight: Radius.circular(10),
                                                bottomRight:
                                                    Radius.circular(10),
                                              ),
                                            ),
                                            child: Text(
                                              snapshot.data!.docs[index]
                                                  ['message'],
                                              style: const TextStyle(
                                                fontSize: 11,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          Text(
                                            DateFormat('dd MMMM yyyy, HH:mm')
                                                .format(
                                              DateTime.parse(
                                                snapshot.data!
                                                    .docs[index]['createddate']
                                                    .toDate()
                                                    .toString(),
                                              ),
                                            ),
                                            style: const TextStyle(
                                              fontSize: 8,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }
                          },
                        );
                      }
                    },
                  )
                : Container(),
            Positioned(
              bottom: 15,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: TextField(
                          controller: _messageController,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            hintText: 'Tulis pesan',
                            hintStyle: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 5),
                    GestureDetector(
                      onTap: () async {
                        if (_messageController.text.isNotEmpty) {
                          FirebaseFirestore.instance.collection('forum').add({
                            'createddate': DateTime.now(),
                            'districtid': int.parse(sharedPreferences!
                                .getString('districtid')
                                .toString()),
                            'message': _messageController.text,
                            'name':
                                sharedPreferences!.getString('name').toString(),
                            'role': 'User App',
                            'subdistrictid': int.parse(sharedPreferences!
                                .getString('subdistrictid')
                                .toString()),
                            'userappid': int.parse(
                                sharedPreferences!.getString('id').toString()),
                            'userid': 0,
                          });

                          await http.post(
                            Uri.parse(API.FORUMSENDMESSAGE),
                            headers: {
                              'Accept': 'application/json',
                            },
                            body: {
                              'userid':
                                  sharedPreferences!.getString('id').toString(),
                              'message': _messageController.text,
                            },
                            encoding: Encoding.getByName('utf-8'),
                          );

                          _messageController.clear();

                          historyScrollController.jumpTo(
                              historyScrollController.position.maxScrollExtent);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          color: primaryColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 15,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
      Container(
        color: Colors.white,
        child: SingleChildScrollView(
          padding:
              const EdgeInsets.symmetric(vertical: 10).copyWith(bottom: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Stack(
                  children: [
                    ClipOval(
                      child: SizedBox(
                        width: 150,
                        height: 150,
                        child: sharedPreferences == null ||
                                sharedPreferences!.getString('profileimage') ==
                                    '' ||
                                sharedPreferences!.getString('profileimage') ==
                                    null
                            ? Image.asset(
                                'images/profile.png',
                                width: 150,
                                height: 150,
                                fit: BoxFit.cover,
                              )
                            : Image.network(
                                '${API.GOOGLEAPIS}/${sharedPreferences!.getString('profileimage')}',
                                width: 150,
                                height: 150,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      right: 15,
                      child: InkWell(
                        child: ClipOval(
                          child: Container(
                            width: 30,
                            height: 30,
                            padding: const EdgeInsets.all(3),
                            decoration: BoxDecoration(
                              color: primaryColor,
                              borderRadius: BorderRadius.circular(50),
                              border: Border.all(
                                color: Colors.white,
                                width: 2,
                              ),
                            ),
                            child: const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.edit_outlined,
                                  size: 15,
                                  color: Colors.white,
                                ),
                              ],
                            ),
                          ),
                        ),
                        onTap: () async {
                          ImagePicker imagePicker = ImagePicker();
                          XFile? image = await imagePicker.pickImage(
                            source: ImageSource.gallery,
                          );

                          if (image != null) {
                            String id =
                                sharedPreferences!.getString('id').toString();

                            final response = http.MultipartRequest(
                              'POST',
                              Uri.parse(API.CHANGEPICTURE),
                            );

                            response.fields['userid'] = id;

                            if (!kIsWeb) {
                              File file = File(image.path);
                              response.files.add(
                                http.MultipartFile(
                                  'picture',
                                  file.readAsBytes().asStream(),
                                  file.lengthSync(),
                                  filename: file.path.split('/').last,
                                ),
                              );
                            } else {
                              response.files.add(http.MultipartFile.fromBytes(
                                  'picture', await image.readAsBytes(),
                                  filename: image.name));
                            }

                            final res = await response.send();
                            final respStr = await res.stream.bytesToString();

                            final respJson = json.decode(respStr);

                            if (respJson['status']) {
                              Fluttertoast.showToast(msg: respJson['message']);

                              sharedPreferences!.setString('profileimage',
                                  respJson['data']['profileimage']);
                            } else {
                              showAlertFailed(context, respJson['message']);
                            }
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),
                    Text(
                      sharedPreferences != null &&
                              sharedPreferences!.getString('name') != null
                          ? sharedPreferences!.getString('name').toString()
                          : 'Guest',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      sharedPreferences != null &&
                              sharedPreferences!.getString('email') != null
                          ? sharedPreferences!.getString('email').toString()
                          : '- Perangkat Desa -',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF888888),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              const Divider(
                thickness: .5,
              ),
              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Pengaturan Akun',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 15),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.person_outline,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Profil',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () async {
                        modalChangeProfile();
                      },
                    ),
                    const SizedBox(height: 10),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.lock_outline,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Ubah Password',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        modalChangePassword();
                      },
                    ),
                    const SizedBox(height: 10),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.access_time_outlined,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Ketersediaan Anggota',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => const MyAvailableMember(),
                        ));
                      },
                    ),
                    const SizedBox(height: 30),
                    const Text(
                      'Aktivitas Harian',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 15),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.manage_history_outlined,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Buat Aktivitas Harian',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () async {
                        ModalDialog modal = ModalDialog(context);

                        modal.showLoadingDialog();

                        final response = await http.post(
                          Uri.parse(API.DAILYACTIVITYGETTODAY),
                          headers: {
                            'Accept': 'application/json',
                          },
                          body: {
                            'userappid':
                                sharedPreferences!.getString('id').toString(),
                          },
                          encoding: Encoding.getByName('UTF-8'),
                        );

                        modal.hideLoadingDialog();

                        var json = jsonDecode(response.body);

                        if (json['status']) {
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => MyAddActivity(
                              activityCount: json['todayactivity'],
                            ),
                          ));
                        } else {
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => MyAddActivity(
                              activityCount: 0,
                            ),
                          ));
                        }
                      },
                    ),
                    const SizedBox(height: 15),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.history_outlined,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Riwayat Aktivitas Harian',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () async {
                        Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => MyHistoryActivity(
                            date:
                                DateFormat('yyyy-MM-dd').format(DateTime.now()),
                          ),
                        ));
                      },
                    ),
                    const SizedBox(height: 30),
                    const Text(
                      'Umum',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 15),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.account_tree_outlined,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Struktur Organisasi Desa',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) =>
                              const MyOrganizationalStructure(),
                        ));
                      },
                    ),
                    const SizedBox(height: 10),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.shield_outlined,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Kebijakan Privasi',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          builder: (context) {
                            return Container(
                              padding: const EdgeInsets.symmetric(vertical: 25),
                              height: MediaQuery.of(context).size.height * .9,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(20),
                                  topRight: Radius.circular(20),
                                ),
                              ),
                              child: Stack(
                                children: [
                                  Column(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 20),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            const Text(
                                              'Kebijakan Privasi',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w700,
                                                fontSize: 18,
                                              ),
                                            ),
                                            InkWell(
                                              child: ClipOval(
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(5),
                                                  decoration:
                                                      const BoxDecoration(
                                                    color: Color(0xFFEDEDED),
                                                  ),
                                                  child: const Icon(
                                                    Icons.close,
                                                    size: 20,
                                                    color: Color(0xFF707070),
                                                  ),
                                                ),
                                              ),
                                              onTap: () {
                                                Navigator.of(context).pop();
                                              },
                                            )
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 10),
                                      const Divider(
                                        thickness: 0.5,
                                      ),
                                      const SizedBox(height: 10),
                                    ],
                                  ),
                                  const Positioned(
                                    top: 60,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    child: Scrollbar(
                                      child: SingleChildScrollView(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 20,
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Sebagai penyedia layanan informasi digital, kami Siades sangat menjunjung tinggi privasi pengguna. Hal ini karena informasi pribadi merupakan hal yang sangat krusial dan tidak boleh diketahui oleh siapapun. Berikut akan kami jelaskan mengenai informasi apa saja yang kami terima dan kami kumpulkan pada saat Anda mengunjungi Aplikasi Sisen. Serta, tentang bagaimana kami menyimpan dan menjaga informasi tersebut. Kami tegaskan bahwa kami tidak pernah memberikan informasi tersebut kepada siapapun.',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF555555),
                                              ),
                                            ),
                                            SizedBox(height: 15),
                                            Text(
                                              'Informasi yang dikumpulkan oleh Siades',
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                            SizedBox(height: 10),
                                            Text(
                                              'Seperti situs dan aplikasi lain pada umumnya, kami mengumpulkan dan menggunakan data yang terdapat pada file log. Informasi terdapat pada file log termasuk alamat IP (Internet Protocol) Anda, ISP (Internet Service Provider), browser yang anda gunakan, waktu pada saat Anda berkunjung serta halaman mana saja yang Anda buka selama berkunjung di Siades. Jika Anda login melalui aplikasi, kami juga mengumpulkan informasi yang kami simpan di Akun Sisen Anda, yang kami anggap sebagai informasi pribadi.',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF555555),
                                              ),
                                            ),
                                            SizedBox(height: 15),
                                            Text(
                                              'Kami Menggunakan Data Untuk Membangun Layanan Yang Lebih Baik',
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                            SizedBox(height: 10),
                                            Text(
                                              'Kami menggunakan informasi Anda untuk memberikan layanan kami, mempertahankan & meningkatkan layanan kami, mengembangkan layanan baru, mengukur performa, berkomunikasi dengan Anda dan melindungi Akun Siades Anda. Data yang dikumpulkan oleh situs dan aplikasi kami tidak akan disebarluaskan ke pihak manapun dan murni akan digunakan secara profesional oleh Sisen.',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF555555),
                                              ),
                                            ),
                                            SizedBox(height: 15),
                                            Text(
                                              'Mengelola, Meninjau dan Memperbarui Informasi Anda',
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                            SizedBox(height: 10),
                                            Text(
                                              'Saat login, Anda dapat meninjau dan memperbarui informasi yang telah dikumpulkan dengan melakukan perubahan di aplikasi Siades',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF555555),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 10),
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          children: [
                            Icon(
                              Icons.list_outlined,
                              size: 20,
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                'Syarat dan Ketentuan',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          builder: (context) {
                            return Container(
                              padding: const EdgeInsets.symmetric(vertical: 25),
                              height: MediaQuery.of(context).size.height * .9,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(20),
                                  topRight: Radius.circular(20),
                                ),
                              ),
                              child: Stack(
                                children: [
                                  Column(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 20),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            const Text(
                                              'Syarat dan Ketentuan',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w700,
                                                fontSize: 18,
                                              ),
                                            ),
                                            InkWell(
                                              child: ClipOval(
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(5),
                                                  decoration:
                                                      const BoxDecoration(
                                                    color: Color(0xFFEDEDED),
                                                  ),
                                                  child: const Icon(
                                                    Icons.close,
                                                    size: 20,
                                                    color: Color(0xFF707070),
                                                  ),
                                                ),
                                              ),
                                              onTap: () {
                                                Navigator.of(context).pop();
                                              },
                                            )
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 10),
                                      const Divider(
                                        thickness: 0.5,
                                      ),
                                      const SizedBox(height: 10),
                                    ],
                                  ),
                                  const Positioned(
                                    top: 60,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    child: Scrollbar(
                                      child: SingleChildScrollView(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 20,
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Dengan mengunduh, memasang, dan/atau menggunakan Platform imuni, Anda menyetujui bahwa Anda telah membaca, memahami, dan menyetujui Syarat dan Ketentuan (“Syarat dan Ketentuan”) ini. Syarat dan Ketentuan ini menandakan perjanjian hukum antara Anda dan imuni dan berlaku untuk kunjungan Anda ke dan penggunaan Anda atas Platform dan Layanan kami (sesuai yang didefinisikan di bawah ini). Namun, harap diperhatikan bahwa syarat, batasan, dan kebijakan privasi tambahan dapat berlaku. Apabila Anda tidak setuju terhadap salah satu, sebagian, atau seluruh isi yang tertuang dalam Ketentuan Penggunaan dan Kebijakan Privasi ini, silakan untuk menghapus Platform dalam perangkat elektronik Anda dan/atau tidak mengakses Platform dan/atau tidak menggunakan Layanan Kami. Mohon untuk dapat diperhatikan pula bahwa Ketentuan Penggunaan dan Kebijakan Privasi dapat diperbarui dari waktu ke waktu.',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF555555),
                                              ),
                                            ),
                                            SizedBox(height: 15),
                                            Text(
                                              'Ketentuan Umum',
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                            SizedBox(height: 10),
                                            Text(
                                              '1.1. Semua referensi di bawah Syarat dan Ketentuan ini untuk “imuni” atau “kami” merujuk ke PT. Imuni Indonesia Sehat dan badan-badan yang terkait (“imuni”), dan semua referensi untuk “Pelanggan” atau “Anda” merujuk ke pengguna Platform dan/atau Layanan yang berwenang.\n\n1.2. Platform adalah aplikasi (versi Android atau iOS), aplikasi web (aplikasi yang dapat diakses menggunakan web), website yang dikelola oleh Kami sebagaimana diperbarui dari waktu ke waktu.\n\n1.3. Platform berfungsi sebagai sarana untuk menghubungkan Anda dengan pihak penyedia layanan atau menjual barang kepada Anda seperti (namun tidak terbatas pada) fasilitas kesehatan, dokter, apotek, laboratorium, dan/atau jasa pengantaran (“Penyedia Layanan”).\n\n1.4. Koneksi internet diperlukan untuk dapat menggunakan Layanan dan biaya terkait penggunaan koneksi internet tersebut ditanggung sepenuhnya oleh Anda.',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF555555),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 25),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                          ),
                          backgroundColor: const Color(0xFFE64805),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                        onPressed: () {
                          showModalBottomSheet(
                            elevation: 0,
                            context: context,
                            isScrollControlled: true,
                            builder: (context) {
                              return Container(
                                width: double.infinity,
                                height: 500,
                                padding:
                                    const EdgeInsets.all(20).copyWith(top: 40),
                                child: Column(
                                  children: [
                                    Image.asset(
                                      'images/logout-question.png',
                                      width: 175,
                                    ),
                                    const SizedBox(height: 20),
                                    const Text(
                                      'Apakah anda yakin ingin keluar?',
                                      style: TextStyle(
                                        fontSize: 22,
                                        fontWeight: FontWeight.w700,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 5),
                                    const Text(
                                      'Jika anda yakin, Anda akan keluar dari sistem dan akan diarahkan ke halaman Login',
                                      style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: Color(0xFF555555)),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 20),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: ElevatedButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  const Color(0xFFCCCCCC),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                            ),
                                            child: const Text(
                                              'Batal',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 10),
                                        Expanded(
                                          child: ElevatedButton(
                                            onPressed: () async {
                                              Navigator.of(context).pop();
                                              sharedPreferences!.clear();

                                              Navigator.of(context)
                                                  .pushReplacement(
                                                      MaterialPageRoute(
                                                builder: (context) =>
                                                    const MyHomePage(),
                                              ));
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  const Color(0xFFE64805),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                            ),
                                            child: const Text(
                                              'Ya, Logout',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              );
                            },
                          );
                        },
                        child: const Text(
                          'Logout',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: const Text(
                  'Versi Aplikasi\nbeta 1.0.0',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF888888),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      )
    ];
  }

  final PersistentTabController _persistentTabController =
      PersistentTabController(initialIndex: 0);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        }

        if (_currentNavbar == 0) {
          SystemNavigator.pop();
        } else {
          _persistentTabController.jumpToTab(0);
        }
      },
      child: Scaffold(
        appBar: _currentNavbar != 0
            ? AppBar(
                surfaceTintColor: Colors.white,
                backgroundColor: primaryColor,
                title: Text(
                  _currentNavbar == 4
                      ? 'Akun'
                      : (_currentNavbar == 3
                          ? 'Forum Diskusi'
                          : (_currentNavbar == 1
                              ? 'Kalender Event'
                              : 'Absensi')),
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                centerTitle: true,
                leading: IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios_new_outlined,
                    color: Colors.white,
                    size: 18,
                  ),
                  onPressed: () {
                    _persistentTabController.jumpToTab(0);

                    setState(() {
                      _currentNavbar = 0;
                    });
                  },
                ),
              )
            : null,
        backgroundColor: Colors.white,
        body: PersistentTabView(
          context,
          screens: _getContent(),
          navBarStyle: NavBarStyle.style15,
          padding: EdgeInsets.all(7),
          decoration: const NavBarDecoration(
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: Colors.grey,
                blurRadius: 1,
              ),
            ],
            borderRadius: BorderRadius.all(Radius.zero),
          ),
          backgroundColor: Colors.white,
          controller: _persistentTabController,
          onItemSelected: (value) async {
            if (!kIsWeb) {
              if (value == 3) {
                _noScreenshot.screenshotOff();
              } else {
                _noScreenshot.screenshotOn();
              }
            }

            if (value == 2) {
              setState(() {
                _currentNavbar = 0;
              });

              _persistentTabController.jumpToTab(0);

              await Navigator.of(context)
                  .push(MaterialPageRoute(
                builder: (context) => MyAbsence(
                  sharedPreferences: sharedPreferences!,
                ),
              ))
                  .then((value) async {
                await getAbsenceStatus();
              });

              return;
            } else {
              setState(() {
                _currentNavbar = value;
              });
            }
          },
          items: [
            PersistentBottomNavBarItem(
              icon: const Icon(Icons.home_outlined),
              title: 'Beranda',
              activeColorPrimary: primaryColor,
              inactiveColorPrimary: Colors.grey,
            ),
            PersistentBottomNavBarItem(
              icon: const Icon(Icons.calendar_month_outlined),
              title: 'Event',
              activeColorPrimary: primaryColor,
              inactiveColorPrimary: Colors.grey,
            ),
            PersistentBottomNavBarItem(
              icon: const Icon(Icons.fingerprint_outlined),
              title: 'Absensi',
              activeColorPrimary: primaryColor,
              inactiveColorPrimary: Colors.grey,
              activeColorSecondary: Colors.white,
            ),
            PersistentBottomNavBarItem(
              icon: const Icon(Icons.chat_outlined),
              title: 'Diskusi',
              activeColorPrimary: primaryColor,
              inactiveColorPrimary: Colors.grey,
            ),
            PersistentBottomNavBarItem(
              icon: const Icon(Icons.account_circle_outlined),
              title: 'Akun',
              activeColorPrimary: primaryColor,
              inactiveColorPrimary: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:convert';
import 'dart:io';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';

class MyAvailableMember extends StatefulWidget {
  const MyAvailableMember({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MyAvailableMember();
  }
}

class _MyAvailableMember extends State<MyAvailableMember> {
  bool available = true;
  TextEditingController unavailableReasonController = TextEditingController();
  List history = [];
  bool historyLoaded = false;
  File? pickedFile;

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      getAvailableMember();
    });
  }

  getAvailableMember() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(
      Uri.parse(API.AVAILABLEMEMBERGET),
      headers: {
        'Accept': 'application/json',
      },
      body: {
        'userid': id,
      },
      encoding: Encoding.getByName('UTF-8'),
    );

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      setState(() {
        available = json['data']['available'] == '1' ? true : false;
        unavailableReasonController.text = json['data']['description'] ?? '';
      });
    }

    getHistory();
  }

  getHistory() async {
    setState(() {
      history = [];
      historyLoaded = false;
    });

    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(Uri.parse(API.AVAILABLEMEMBERHISTORY),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userappid': id,
        },
        encoding: Encoding.getByName('UTF-8'));

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      setState(() {
        history = json['data'];
        historyLoaded = true;
      });
    }
  }

  String formatDateTodayInIndonesian({date}) {
    // Define Indonesian day names and month names
    final List<String> dayNames = [
      'Senin',
      'Selasa',
      'Rabu',
      'Kamis',
      'Jumat',
      'Sabtu',
      'Minggu',
    ];
    final List<String> monthNames = [
      '',
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember'
    ];

    // Get current date
    DateTime? now;
    if (date != null) {
      now = date;
    } else {
      now = DateTime.now();
    }

    // Format the date
    String dayName = dayNames[now!.weekday - 1];
    String day = DateFormat.d().format(now);
    String month = monthNames[now.month];
    String year = DateFormat.y().format(now);

    // Construct the formatted date string
    String formattedDate = '$dayName, $day $month $year';

    return formattedDate;
  }

  doSave({showMsg = false}) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    var response =
        http.MultipartRequest('POST', Uri.parse(API.AVAILABLEMEMBER));
    response.fields['userid'] = id!;
    response.fields['available'] = available ? '1' : '0';
    response.fields['description'] = unavailableReasonController.text;

    if (pickedFile != null) {
      response.files.add(http.MultipartFile.fromBytes(
        'document',
        await pickedFile!.readAsBytes(),
        filename: pickedFile!.path.split('/').last,
      ));
    }

    var res = await response.send();
    var resStr = await res.stream.bytesToString();

    modalDialog.hideLoadingDialog();

    if (showMsg) {
      var json = jsonDecode(resStr);

      if (json['status']) {
        Fluttertoast.showToast(msg: json['message']);
      } else {
        showAlertFailed(context, json['message']);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          backgroundColor: primaryColor,
          centerTitle: true,
          title: const Text(
            'Ketersediaan Anggota',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          leading: IconButton(
            icon: const Icon(
              CupertinoIcons.back,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          )),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20)
            .copyWith(bottom: 25, top: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            available
                ? SizedBox(
                    width: double.infinity,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'images/happy.png',
                          width: 200,
                        ),
                        const SizedBox(height: 5),
                        const Text(
                          'Mantap! Kamu sudah masuk kantor',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: primaryColor,
                          ),
                        ),
                      ],
                    ),
                  )
                : SizedBox(
                    width: double.infinity,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'images/adventure.png',
                          width: 250,
                        ),
                        const SizedBox(height: 5),
                        const Text(
                          'Dimana? Kamu belum masuk kantor nih!',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
            const SizedBox(height: 10),
            const Divider(
              thickness: .5,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Ketersediaan Anggota di Kantor',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Transform.scale(
                  scale: .5,
                  child: SizedBox(
                    width: 50,
                    child: Switch(
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      activeColor: primaryColor,
                      inactiveTrackColor: const Color(0xFFE5E5E5),
                      value: available,
                      onChanged: (value) {
                        setState(() {
                          available = value;

                          if (available) {
                            unavailableReasonController.text = '';
                            doSave();
                          }
                        });
                      },
                    ),
                  ),
                )
              ],
            ),
            !available
                ? Column(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: TextField(
                          controller: unavailableReasonController,
                          textAlignVertical: TextAlignVertical.top,
                          maxLines: 1,
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 10,
                            ),
                            hintText: 'Keterangan',
                            hintStyle: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF707070),
                              fontWeight: FontWeight.w400,
                            ),
                            border: InputBorder.none,
                            filled: true,
                            fillColor: Color(0xFFEDEDED),
                          ),
                          onChanged: (val) {},
                        ),
                      ),
                      const SizedBox(height: 10),
                      InkWell(
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEDEDED),
                            borderRadius: BorderRadius.circular(5),
                          ),
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.upload_outlined,
                                color: Color(0xFF999999),
                                size: 30,
                              ),
                              const SizedBox(height: 5),
                              Text(
                                pickedFile == null
                                    ? 'Upload Dokumen Pendukung (Opsional)'
                                    : pickedFile!.path.split('/').last,
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: Color(0xFF999999),
                                  fontWeight: FontWeight.w400,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        onTap: () async {
                          FilePickerResult? result =
                              await FilePicker.platform.pickFiles(
                            type: FileType.custom,
                            allowedExtensions: ['pdf', 'doc', 'docx'],
                          );

                          if (result != null) {
                            setState(() {
                              pickedFile = File(result.files.single.path!);
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 10),
                    ],
                  )
                : Container(),
            !available
                ? Column(
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              vertical: 15,
                            ),
                            backgroundColor: primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                          onPressed: () async {
                            doSave(showMsg: true);
                          },
                          child: const Text(
                            'Simpan Pengaturan',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                    ],
                  )
                : Container(),
            const Divider(
              thickness: .5,
            ),
            const SizedBox(height: 10),
            const Text(
              'Log Ketersediaan Anggota',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            historyLoaded
                ? history.isNotEmpty
                    ? Column(
                        children: history.map((e) {
                          return Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(13),
                            margin: const EdgeInsets.only(bottom: 15),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(5),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  spreadRadius: 1,
                                  blurRadius: 1,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 5),
                                      decoration: BoxDecoration(
                                        color: e['available'] == '1'
                                            ? Colors.greenAccent
                                            : Colors.redAccent,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: Text(
                                        e['available'] == '1'
                                            ? 'Tersedia'
                                            : 'Tidak Tersedia',
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                          color: e['available'] == '1'
                                              ? Colors.black
                                              : Colors.white,
                                        ),
                                      ),
                                    ),
                                    const Spacer(),
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.access_time_outlined,
                                          size: 12,
                                          color: Colors.grey,
                                        ),
                                        const SizedBox(width: 5),
                                        Text(
                                          '${formatDateTodayInIndonesian(date: DateTime.parse(e['createddate']))} ${DateFormat.Hm().format(DateTime.parse(e['createddate']))}',
                                          style: const TextStyle(
                                            fontSize: 10,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                                const Divider(
                                  thickness: .5,
                                ),
                                Text(
                                  e['available'] == '1'
                                      ? 'Anggota berada di kantor'
                                      : e['unavailable_description'] ?? '-',
                                  style: const TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      )
                    : Container(
                        margin: const EdgeInsets.only(top: 40),
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'images/no-data.png',
                              width: 100,
                            ),
                            const SizedBox(height: 10),
                            const Text(
                              'Tidak ada data',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF555555),
                              ),
                            ),
                          ],
                        ),
                      )
                : Column(
                    children: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((e) {
                      return Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(13),
                        margin: const EdgeInsets.only(bottom: 15),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              spreadRadius: 1,
                              blurRadius: 1,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: 75,
                                  height: 15,
                                  child: Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                SizedBox(
                                  width: 100,
                                  height: 15,
                                  child: Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Divider(
                              thickness: .5,
                            ),
                            SizedBox(
                              width: 150,
                              height: 15,
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
          ],
        ),
      ),
    );
  }
}

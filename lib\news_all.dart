import 'dart:convert';
import 'dart:io';

import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:siades/news.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart' as intl;
import 'package:shared_preferences/shared_preferences.dart';

import 'api.dart';

class MyNewsAll extends StatefulWidget {
  const MyNewsAll({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MyNewsAll();
  }
}

class _MyNewsAll extends State<MyNewsAll> {
  int _currentSlider = 0;
  final CarouselSliderController _sliderController = CarouselSliderController();
  List _news = [];
  List _allNews = [];
  List _categoryNews = [];

  String? _currentCategory;

  void getHighlight() async {
    try {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
      String? id = sharedPreferences.getString('id');

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.NEWSGET),
        headers: {'Accept': 'application/json'},
        body: {
          'userid': id,
          'limit': '6',
        },
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        setState(() {
          _news = json['data'];
        });
      }
    } on HandshakeException catch (e) {
      getHighlight();
    }
  }

  void getNews(String category) async {
    try {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
      String? id = sharedPreferences.getString('id');

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.NEWSGET),
        body: {
          'userid': id,
          'categoryid': category,
        },
        encoding: Encoding.getByName('utf-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      setState(() {
        _allNews = json['data'];
      });
    } on HandshakeException catch (e) {
      getNews(category);
    }
  }

  void getCategoryNews() async {
    try {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
      String? id = sharedPreferences.getString('id');

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.NEWSCATEGORY),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': id,
        },
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      setState(() {
        _categoryNews = json['data'];

        if (_categoryNews.isNotEmpty) {
          _currentCategory = json['data'][0]['id'];
          getNews(_currentCategory!);
        }
      });
    } on HandshakeException catch (e) {
      getCategoryNews();
    }
  }

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      getHighlight();
      getCategoryNews();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: const Text(
          'Berita Selengkapnya',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF333333),
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Highlight Berita Terkini',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 15),
                  CarouselSlider(
                    carouselController: _sliderController,
                    items: _news.map((e) {
                      return Builder(
                        builder: (context) {
                          return InkWell(
                            child: SizedBox(
                              width: MediaQuery.of(context).size.width,
                              child: Stack(
                                children: [
                                  Positioned(
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: Image.network(
                                        '${API.GOOGLEAPIS}/${e['thumbnail']}',
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.5),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    bottom: 20,
                                    left: 20,
                                    right: 20,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          e['categoryname'],
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w400,
                                            color: Colors.white,
                                          ),
                                        ),
                                        Text(
                                          e['name'],
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            onTap: () {
                              Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) => MyNews(data: e),
                              ));
                            },
                          );
                        },
                      );
                    }).toList(),
                    options: CarouselOptions(
                      height: 175,
                      viewportFraction: 1,
                      autoPlay: true,
                      autoPlayAnimationDuration:
                          const Duration(milliseconds: 500),
                      onPageChanged: (index, reason) {
                        setState(() {
                          _currentSlider = index;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _news.asMap().entries.map((i) {
                return GestureDetector(
                  onTap: () => _sliderController.animateToPage(i.key),
                  child: Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentSlider == i.key
                          ? primaryColor
                          : const Color(0xFFD9D9D9),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _categoryNews.map((e) {
                  return Container(
                    margin: const EdgeInsets.only(right: 10),
                    child: InkWell(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 5),
                        decoration: BoxDecoration(
                          color: e['id'] == _currentCategory
                              ? primaryColor
                              : const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Text(
                          e['name'],
                          style: TextStyle(
                            fontSize: 11,
                            color: e['id'] == _currentCategory
                                ? Colors.white
                                : const Color(0xFF707070),
                          ),
                        ),
                      ),
                      onTap: () {
                        setState(() {
                          _currentCategory = e['id'];

                          getNews(_currentCategory!);
                        });
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 20),
            _allNews.isNotEmpty
                ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: _allNews.map((e) {
                        return InkWell(
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 100,
                                    height: 100,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      image: DecorationImage(
                                        image: NetworkImage(
                                            '${API.GOOGLEAPIS}/${e['thumbnail']}'),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          e['name'],
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                        const SizedBox(height: 5),
                                        Text(
                                          intl.DateFormat(
                                                  'dd MMMM yyyy, HH:mm WIB')
                                              .format(DateTime.parse(
                                                  e['createddate'] ??
                                                      DateTime.now()
                                                          .toString())),
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                              const SizedBox(height: 15)
                            ],
                          ),
                          onTap: () {
                            Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => MyNews(
                                data: e,
                              ),
                            ));
                          },
                        );
                      }).toList(),
                    ),
                  )
                : Container(
                    margin: const EdgeInsets.only(top: 40),
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'images/searching.png',
                          width: 200,
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          'Tidak ada berita',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: 3),
                        const Text(
                          'Silahkan coba lagi di kemudian hari',
                          style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF555555)),
                        ),
                      ],
                    ),
                  )
          ],
        ),
      ),
    );
  }
}

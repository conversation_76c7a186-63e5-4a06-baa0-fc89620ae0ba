import 'dart:convert';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:url_launcher/url_launcher.dart';

class MyHistoryActivity extends StatefulWidget {
  String date;

  MyHistoryActivity({super.key, required this.date});

  @override
  State<StatefulWidget> createState() {
    return _MyHistoryActivity(date: date);
  }
}

class _MyHistoryActivity extends State<MyHistoryActivity> {
  String date;
  DateTime? _currentDateFilter;
  List historyActivity = [];
  bool historyLoaded = false;

  _MyHistoryActivity({required this.date});

  @override
  void initState() {
    _currentDateFilter = DateTime.parse(date);

    super.initState();

    Future.delayed(Duration.zero, () {
      getHistoryActivity(date: date);
    });
  }

  getHistoryActivity({date}) async {
    setState(() {
      historyActivity = [];

      historyLoaded = false;
    });

    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(
      Uri.parse(API.DAILYACTIVITYGET),
      headers: {
        'Accept': 'application/json',
      },
      body: {'userappid': id, 'date': date ?? ''},
      encoding: Encoding.getByName('UTF-8'),
    );

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      setState(() {
        historyActivity = json['data'];
        historyLoaded = true;
      });
    } else {
      showAlertFailed(context, json['message']);
    }
  }

  String formatDateTodayInIndonesian({date}) {
    // Define Indonesian day names and month names
    final List<String> dayNames = [
      'Senin',
      'Selasa',
      'Rabu',
      'Kamis',
      'Jumat',
      'Sabtu',
      'Minggu',
    ];
    final List<String> monthNames = [
      '',
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember'
    ];

    // Get current date
    DateTime? now;
    if (date != null) {
      now = date;
    } else {
      now = DateTime.now();
    }

    // Format the date
    String dayName = dayNames[now!.weekday - 1];
    String day = DateFormat.d().format(now);
    String month = monthNames[now.month];
    String year = DateFormat.y().format(now);

    // Construct the formatted date string
    String formattedDate = '$dayName, $day $month $year';

    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: primaryColor,
        centerTitle: true,
        title: const Text(
          'Riwayat Aktivitas Harian',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(
            CupertinoIcons.back,
            color: Colors.white,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20)
            .copyWith(top: 20, bottom: 25),
        child: Column(
          children: [
            InkWell(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 15,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFEEEEEE).withOpacity(.75),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.calendar_today_outlined,
                          color: primaryColor,
                          size: 20,
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        Text(
                          formatDateTodayInIndonesian(
                            date: _currentDateFilter,
                          ),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down_outlined,
                      size: 20,
                      color: primaryColor,
                    )
                  ],
                ),
              ),
              onTap: () {
                showModalBottomSheet(
                  isScrollControlled: true,
                  context: context,
                  builder: (context) {
                    DateTime selectedDay_ = _currentDateFilter!;

                    return StatefulBuilder(
                      builder: (context, setState) {
                        return Container(
                          height: 500,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                          ),
                          child: Column(
                            children: [
                              TableCalendar(
                                focusedDay: selectedDay_,
                                firstDay: DateTime(1970),
                                lastDay: DateTime.now(),
                                currentDay: selectedDay_,
                                calendarFormat: CalendarFormat.month,
                                headerStyle: const HeaderStyle(
                                  formatButtonVisible: false,
                                ),
                                calendarStyle: CalendarStyle(
                                  todayDecoration: BoxDecoration(
                                    border: Border.all(
                                      color: const Color(
                                        0xFF941EEC,
                                      ),
                                      width: 1,
                                    ),
                                    shape: BoxShape.circle,
                                  ),
                                  todayTextStyle: const TextStyle(
                                    color: primaryColor,
                                  ),
                                ),
                                onDaySelected: (selectedDay, focusedDay) {
                                  setState(() {
                                    selectedDay_ = selectedDay;
                                  });
                                },
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 10,
                                  horizontal: 20,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    InkWell(
                                      child: const Text(
                                        'Batal',
                                        style: TextStyle(
                                          color: primaryColor,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                    ),
                                    const SizedBox(
                                      width: 20,
                                    ),
                                    InkWell(
                                      child: const Text(
                                        'Pilih',
                                        style: TextStyle(
                                          color: primaryColor,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      onTap: () async {
                                        Navigator.of(context).pop();

                                        setState(() {
                                          _currentDateFilter = selectedDay_;
                                        });

                                        await getHistoryActivity(
                                            date: DateFormat('yyyy-MM-dd')
                                                .format(selectedDay_));
                                      },
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
            Divider(
              thickness: .5,
              color: const Color(0xFF555555).withOpacity(.25),
            ),
            historyLoaded
                ? historyActivity.isNotEmpty
                    ? Column(
                        children: historyActivity.map((e) {
                          return Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(13),
                            margin: const EdgeInsets.only(bottom: 15),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(5),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  spreadRadius: 1,
                                  blurRadius: 1,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 5),
                                      decoration: BoxDecoration(
                                        color: primaryColor,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: const Text(
                                        'Aktivitas',
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    const Spacer(),
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.access_time_outlined,
                                          size: 12,
                                          color: Colors.grey,
                                        ),
                                        const SizedBox(width: 5),
                                        Text(
                                          formatDateTodayInIndonesian(
                                            date: DateTime.parse(
                                              e['createddate'],
                                            ),
                                          ),
                                          style: const TextStyle(
                                            fontSize: 10,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                                const Divider(
                                  thickness: .5,
                                ),
                                Text(
                                  e['activity'],
                                  style: const TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                                e['document'] != null
                                    ? InkWell(
                                        child: Container(
                                          margin:
                                              const EdgeInsets.only(top: 10),
                                          padding: const EdgeInsets.all(10),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFEDEDED),
                                            borderRadius:
                                                BorderRadius.circular(5),
                                          ),
                                          child: Row(
                                            children: [
                                              const Icon(
                                                Icons.attach_file_outlined,
                                                size: 12,
                                                color: Colors.grey,
                                              ),
                                              const SizedBox(width: 5),
                                              Text(
                                                e['document'],
                                                style: const TextStyle(
                                                  fontSize: 10,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        onTap: () async {
                                          String url =
                                              "${API.GOOGLEAPIS}/${e['document']}";
                                          if (!await launchUrl(
                                              Uri.parse(url))) {
                                            showAlertFailed(
                                                context, 'Gagal membuka file');
                                          }
                                        },
                                      )
                                    : Container(),
                              ],
                            ),
                          );
                        }).toList(),
                      )
                    : Container(
                        margin: const EdgeInsets.only(top: 40),
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'images/no-data.png',
                              width: 100,
                            ),
                            const SizedBox(height: 10),
                            const Text(
                              'Tidak ada data',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF555555),
                              ),
                            ),
                          ],
                        ),
                      )
                : Column(
                    children: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((e) {
                      return Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(13),
                        margin: const EdgeInsets.only(bottom: 15),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(5),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              spreadRadius: 1,
                              blurRadius: 1,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: 75,
                                  height: 15,
                                  child: Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                SizedBox(
                                  width: 100,
                                  height: 15,
                                  child: Shimmer.fromColors(
                                    baseColor: Colors.grey[300]!,
                                    highlightColor: Colors.grey[100]!,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Divider(
                              thickness: .5,
                            ),
                            SizedBox(
                              width: 150,
                              height: 15,
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  )
          ],
        ),
      ),
    );
  }
}

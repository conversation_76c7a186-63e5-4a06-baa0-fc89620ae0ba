import 'package:siades/fetchlistofattendance.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:qr_code_dart_scan/qr_code_dart_scan.dart';

class MyListOfAttendance extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _MyListOfAttendance();
  }
}

class _MyListOfAttendance extends State<MyListOfAttendance> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: QRCodeDartScanView(
        onCapture: (data) {
          Navigator.of(context).pushReplacement(MaterialPageRoute(
            builder: (context) => MyFetchListOfAttendance(id: data.toString()),
          ));
        },
      ),
    );
  }
}

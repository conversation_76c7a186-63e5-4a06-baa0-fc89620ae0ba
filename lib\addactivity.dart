import 'dart:convert';
import 'dart:io';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/historyactivity.dart';
import 'package:siades/modal.dart';
import 'package:detect_fake_location/detect_fake_location.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class MyAddActivity extends StatefulWidget {
  int activityCount;

  MyAddActivity({super.key, required this.activityCount});

  @override
  State<StatefulWidget> createState() {
    return _MyAddActivity();
  }
}

class _MyAddActivity extends State<MyAddActivity> {
  TextEditingController activityDescription = TextEditingController();
  File? pickedFile;
  SharedPreferences? sharedPreferences;
  XFile? pickedXFile;
  List<File> files = [];

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      SharedPreferences.getInstance().then((value) {
        setState(() {
          sharedPreferences = value;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          backgroundColor: primaryColor,
          centerTitle: true,
          title: const Text(
            'Buat Aktivitas Harian',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          leading: IconButton(
            icon: const Icon(
              CupertinoIcons.back,
              color: Colors.white,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          )),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20)
            .copyWith(bottom: 25, top: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10).copyWith(left: 20, right: 20),
              decoration: BoxDecoration(
                color: const Color(0xFFEDEDED),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                children: [
                  ClipOval(
                    child: SizedBox(
                      width: 75,
                      height: 75,
                      child: sharedPreferences == null ||
                              sharedPreferences!.getString('profileimage') ==
                                  '' ||
                              sharedPreferences!.getString('profileimage') ==
                                  null
                          ? Image.asset(
                              'images/profile.png',
                              width: 75,
                              height: 75,
                              fit: BoxFit.cover,
                            )
                          : Image.network(
                              '${API.GOOGLEAPIS}/${sharedPreferences!.getString('profileimage')}',
                              width: 75,
                              height: 75,
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sharedPreferences == null
                            ? 'Guest'
                            : sharedPreferences!.getString('name')!,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      // Text(
                      //   sharedPreferences == null
                      //       ? '-'
                      //       : 'NIP. ${sharedPreferences!.getString('nip')!}',
                      //   style: const TextStyle(
                      //     fontSize: 10,
                      //     fontWeight: FontWeight.w400,
                      //   ),
                      // ),
                      Text(
                        sharedPreferences == null
                            ? '-'
                            : sharedPreferences!.getString('position')!,
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
            const SizedBox(height: 10),
            ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: TextField(
                controller: activityDescription,
                textAlignVertical: TextAlignVertical.top,
                maxLines: 5,
                decoration: InputDecoration(
                  contentPadding:
                      const EdgeInsets.all(10).copyWith(left: 15, right: 15),
                  hintText: 'Ketik aktivitas anda disini ...',
                  hintStyle: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF707070),
                    fontWeight: FontWeight.w400,
                  ),
                  border: InputBorder.none,
                  filled: true,
                  fillColor: const Color(0xFFEDEDED),
                ),
                minLines: 5,
              ),
            ),
            const SizedBox(height: 10),
            InkWell(
              child: Container(
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: const Color(0xFFEDEDED),
                  borderRadius: BorderRadius.circular(5),
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.upload_outlined,
                      color: Color(0xFF999999),
                      size: 30,
                    ),
                    const SizedBox(height: 5),
                    Text(
                      pickedFile == null
                          ? 'Upload Foto Pendukung (Opsional)'
                          : pickedFile!.path.split('/').last,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              onTap: () async {
                XFile? imagePicker = await ImagePicker().pickImage(
                  source: ImageSource.gallery,
                );

                if (imagePicker != null) {
                  setState(() {
                    pickedFile = File(imagePicker.path);
                    pickedXFile = imagePicker;
                  });
                }
              },
            ),
            const SizedBox(height: 5),
            const Text(
              '*Batas ukuran file: 3MB',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: 15,
            ),
            Text(
              'Dokumen pendukung lainnya',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 10,
            ),
            Row(
              children: files.map(
                    (e) {
                      return Container(
                        margin: const EdgeInsets.only(right: 5),
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: Stack(
                            children: [
                              Center(
                                child: Icon(
                                  e.path.split('/').last.split('.').last ==
                                          'pdf'
                                      ? Icons.picture_as_pdf
                                      : Icons.image,
                                  color: Colors.grey,
                                ),
                              ),
                              Positioned(
                                right: 0,
                                top: 0,
                                child: InkWell(
                                  child: Container(
                                    width: 15,
                                    height: 15,
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                    ),
                                    child: Center(
                                      child: Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 10,
                                      ),
                                    ),
                                  ),
                                  onTap: () {
                                    setState(() {
                                      files.remove(e);
                                    });
                                  },
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList() +
                  [
                    Container(
                      child: InkWell(
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: Center(
                            child: Icon(Icons.add, color: Colors.grey),
                          ),
                        ),
                        onTap: () async {
                          FilePickerResult? result =
                              await FilePicker.platform.pickFiles(
                            type: FileType.custom,
                            allowedExtensions: [
                              'pdf',
                              'doc',
                              'docx',
                              'jpg',
                              'jpeg',
                              'png'
                            ],
                          );

                          if (result != null) {
                            File file = File(result.files.single.path!);

                            setState(() {
                              files.add(file);
                            });
                          }
                        },
                      ),
                    )
                  ],
            ),
            const SizedBox(height: 15),
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    vertical: 15,
                  ),
                  backgroundColor: primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                onPressed: () async {
                  SharedPreferences sharedPreferences =
                      await SharedPreferences.getInstance();
                  String? id = sharedPreferences.getString('id');

                  ModalDialog modalDialog = ModalDialog(context);
                  modalDialog.showLoadingDialog();

                  bool serviceEnabled;
                  LocationPermission permission;

                  serviceEnabled = await Geolocator.isLocationServiceEnabled();
                  if (!serviceEnabled) {
                    modalDialog.hideLoadingDialog();
                    showAlertFailed(context, 'Please enable location service');
                    return;
                  }

                  permission = await Geolocator.checkPermission();
                  if (permission == LocationPermission.denied) {
                    permission = await Geolocator.requestPermission();

                    if (permission == LocationPermission.denied) {
                      modalDialog.hideLoadingDialog();
                      showAlertFailed(context, 'Please enable location service');
                      return;
                    }
                  }

                  if (permission == LocationPermission.deniedForever) {
                    modalDialog.hideLoadingDialog();
                    showAlertFailed(context, 'Please enable location service');
                    return;
                  }

                  if (!kIsWeb) {
                    bool isFakeLocation =
                        await DetectFakeLocation().detectFakeLocation();

                    if (isFakeLocation) {
                      modalDialog.hideLoadingDialog();
                      showAlertFailed(context, 'Fake location detected');
                      return;
                    }
                  }

                  Position position = await Geolocator.getCurrentPosition(
                    desiredAccuracy: LocationAccuracy.best,
                  );

                  var request = http.MultipartRequest(
                      'POST', Uri.parse(API.DAILYACTIVITYADD));

                  if (pickedFile != null || pickedXFile != null) {
                    if (!kIsWeb) {
                      request.files.add(await http.MultipartFile.fromPath(
                          'document', pickedFile!.path));
                    } else {
                      request.files.add(http.MultipartFile.fromBytes(
                          'document', await pickedXFile!.readAsBytes(),
                          filename: pickedXFile!.name));
                    }
                  }

                  for (var file in files) {
                    if (!kIsWeb) {
                      request.files.add(await http.MultipartFile.fromPath(
                          'other_document[]', file.path));
                    } else {
                      request.files.add(http.MultipartFile.fromBytes(
                          'other_document[]', await file.readAsBytes(),
                          filename: file.path.split('/').last));
                    }
                  }

                  request.fields['userappid'] = id!;
                  request.fields['activity'] = activityDescription.text;
                  request.fields['longitude'] = position.longitude.toString();
                  request.fields['latitude'] = position.latitude.toString();

                  var response = await request.send();
                  var responseData = await response.stream.bytesToString();

                  modalDialog.hideLoadingDialog();

                  var json = jsonDecode(responseData);

                  if (json['status']) {
                    Fluttertoast.showToast(msg: json['message']);
                    Navigator.of(context).pop();
                  } else {
                    showAlertFailed(context, json['message']);
                  }
                },
                child: const Text(
                  'Posting',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.only(top: 10),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: const Color(0xFFEDEDED).withOpacity(.8),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.history_outlined,
                        color: Color(0xFF707070),
                        size: 20,
                      ),
                      const SizedBox(width: 5),
                      Text(
                        'Aktivitas hari ini: ${widget.activityCount}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF707070),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  InkWell(
                    child: const Text(
                      'Lihat Aktivitas',
                      style: TextStyle(
                        fontSize: 12,
                        color: primaryColor,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => MyHistoryActivity(
                          date: DateFormat('yyyy-MM-dd').format(DateTime.now()),
                        ),
                      ));
                    },
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

class MyNews extends StatefulWidget {
  Map data;

  MyNews({super.key, required this.data});

  @override
  State<StatefulWidget> createState() {
    return _MyNews();
  }
}

class _MyNews extends State<MyNews> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          widget.data['name'],
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20)
            .copyWith(bottom: 30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10).copyWith(top: 5, bottom: 5),
              decoration: BoxDecoration(
                color: primaryColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                widget.data['categoryname'],
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 10),
            Text(
              widget.data['name'],
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w700),
            ),
            const SizedBox(height: 15),
            ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: Image.network(
                "${API.GOOGLEAPIS}/${widget.data['thumbnail']}",
                width: MediaQuery.of(context).size.width,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(height: 20),
            HtmlWidget(
              widget.data['description'],
              textStyle: const TextStyle(fontSize: 12),
            )
          ],
        ),
      ),
    );
  }
}

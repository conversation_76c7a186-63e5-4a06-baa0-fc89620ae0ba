import 'dart:convert';
import 'dart:io';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/dashboard.dart';
import 'package:siades/firebase_options.dart';
import 'package:siades/modal.dart';
import 'package:siades/splashscreen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  FirebaseMessaging messaging = FirebaseMessaging.instance;

  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    print('Got a message whilst in the foreground!');
    print('Message data: ${message.data}');

    if (message.notification != null) {
      print('Message also contained a notification: ${message.notification}');
    }
  });

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: primaryColor,
      statusBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const MyApp());
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  print('Handling a background message ${message.messageId}');
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SIADES - ABSENSI DIGITAL PERANGKAT DESA',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: primaryColor),
        useMaterial3: true,
        textTheme: GoogleFonts.montserratTextTheme(),
      ),
      debugShowCheckedModeBanner: false,
      home: const MySplashscreen(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () async {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();

      if (sharedPreferences.getString('id') != null) {
        Navigator.of(context).pushReplacement(MaterialPageRoute(
          builder: (context) => const MyDashboard(),
        ));
      }
    });
  }

  doLogin() async {
    try {
      if (usernameController.text.isEmpty) {
        showAlertFailed(context, 'Username tidak boleh kosong');
        return;
      }

      if (passwordController.text.isEmpty) {
        showAlertFailed(context, 'Password tidak boleh kosong');
        return;
      }

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.LOGIN),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'username': usernameController.text,
          'password': passwordController.text,
        },
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        SharedPreferences sharedPreferences =
            await SharedPreferences.getInstance();
        sharedPreferences.setString('id', json['data']['id']);
        // sharedPreferences.setString('nip', json['data']['nip'] ?? '');
        sharedPreferences.setString('name', json['data']['name']);
        sharedPreferences.setString('username', json['data']['username']);
        sharedPreferences.setString('email', json['data']['email']);
        sharedPreferences.setString(
            'profileimage', json['data']['profileimage'] ?? '');
        sharedPreferences.setString(
            'subdistrictid', json['data']['subdistrictid'].toString());
        sharedPreferences.setString(
            'districtid', json['data']['districtid'].toString());
        sharedPreferences.setString('position', json['data']['position'] ?? '');
        sharedPreferences.setString('token', json['data']['token'].toString());

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const MyDashboard(),
          ),
        );
      } else {
        showAlertFailed(context, json['message']);
      }
    } on HandshakeException catch (e) {
      doLogin();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'images/siades.png',
                width: 100,
              ),
              const SizedBox(height: 20),
              const Text(
                'SIADES - ABSENSI DIGITAL PERANGKAT DESA',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF333333),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 5,
              ),
              const Text(
                'Gunakan username dan password anda untuk masuk kedalam dashboard utama',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF7B7B7B),
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Username',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: TextField(
                        controller: usernameController,
                        textAlignVertical: TextAlignVertical.top,
                        maxLines: 1,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 10,
                          ),
                          hintText: 'Masukkan Username',
                          hintStyle: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF707070),
                            fontWeight: FontWeight.w400,
                          ),
                          border: InputBorder.none,
                          filled: true,
                          fillColor: Color(0xFFEDEDED),
                        ),
                        onChanged: (val) {},
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    const Text(
                      'Password',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: TextField(
                        controller: passwordController,
                        obscureText: true,
                        textAlignVertical: TextAlignVertical.top,
                        maxLines: 1,
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 10,
                          ),
                          hintText: 'Masukkan Password',
                          hintStyle: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF707070),
                            fontWeight: FontWeight.w400,
                          ),
                          border: InputBorder.none,
                          filled: true,
                          fillColor: Color(0xFFEDEDED),
                        ),
                        onChanged: (val) {},
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            vertical: 15,
                          ),
                          backgroundColor: primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                        onPressed: () async {
                          doLogin();
                        },
                        child: const Text(
                          'Login ke Dashboard',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

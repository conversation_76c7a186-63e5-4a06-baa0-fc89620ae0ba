import 'package:siades/color.dart';
import 'package:siades/main.dart';
import 'package:flutter/material.dart';

class MySplashscreen extends StatefulWidget {
  const MySplashscreen({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MySplashscreen();
  }
}

class _MySplashscreen extends State<MySplashscreen>
    with TickerProviderStateMixin {
  double scaleAnimated = .5;
  double scaleLogo = 0;
  double animateOpacity = 0;
  double animatePosition = 0;
  AnimationController? animatedContainer;
  AnimationController? animatedLogo;
  AnimationController? animatedOpacity;
  AnimationController? animatedPosition;

  @override
  void initState() {
    animatedContainer = AnimationController(
        vsync: this,
        lowerBound: .5,
        upperBound: 2.5,
        duration: const Duration(milliseconds: 500));
    animatedContainer!.addListener(() {
      setState(() {
        scaleAnimated = animatedContainer!.value;
      });
    });

    animatedLogo = AnimationController(
        vsync: this,
        lowerBound: 0,
        upperBound: 1,
        duration: const Duration(milliseconds: 750));
    animatedLogo!.addListener(() {
      setState(() {
        scaleLogo = animatedLogo!.value;
      });
    });
    animatedLogo!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        animatedOpacity!.forward(from: 0);
        animatedPosition!.forward(from: 0);
      }
    });

    animatedOpacity = AnimationController(
        vsync: this,
        lowerBound: 0,
        upperBound: 1,
        animationBehavior: AnimationBehavior.preserve,
        duration: const Duration(milliseconds: 250));
    animatedOpacity!.addListener(() {
      setState(() {
        animateOpacity = animatedOpacity!.value;
      });
    });

    animatedPosition = AnimationController(
        vsync: this,
        lowerBound: 0,
        upperBound: 215,
        animationBehavior: AnimationBehavior.preserve,
        duration: const Duration(milliseconds: 250));
    animatedPosition!.addListener(() {
      setState(() {
        animatePosition = animatedPosition!.value;
      });
    });

    super.initState();

    Future.delayed(Duration(milliseconds: 250), () {
      animatedContainer!.forward(from: 0.5);
      animatedLogo!.forward(from: 0);
    });

    animatedPosition!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(
          const Duration(seconds: 3),
          () {
            Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => const MyHomePage(),
            ));
          },
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Transform.scale(
            scale: scaleAnimated,
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: primaryColor,
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Transform.scale(
                  scale: scaleLogo,
                  child: Image.asset(
                    'images/siades.png',
                    width: 125,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: animatePosition,
            bottom: 0,
            left: 0,
            right: 0,
            child: Opacity(
              opacity: animateOpacity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Absensi Digital Perangkat Desa',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  CircularProgressIndicator(
                    color: Colors.white,
                  )
                ],
              ),
            ),
          )
          // Image.asset(
          //   'images/tugu.jpeg',
          //   height: MediaQuery.of(context).size.height,
          //   fit: BoxFit.cover,
          // ),
          // Positioned(
          //   top: 0,
          //   bottom: 0,
          //   left: 0,
          //   right: 0,
          //   child: Container(
          //     decoration: const BoxDecoration(
          //       gradient: LinearGradient(
          //         colors: [
          //           Colors.transparent,
          //           Colors.transparent,
          //           Color(0xFF0C0C0C),
          //           Color(0xFF0C0C0C),
          //         ],
          //         begin: Alignment.topCenter,
          //         end: Alignment.bottomCenter,
          //       ),
          //     ),
          //   ),
          // ),
          // Positioned(
          //   bottom: 40,
          //   left: 25,
          //   right: 25,
          //   child: Column(
          //     children: [
          //       Stack(
          //         clipBehavior: Clip.none,
          //         children: [
          //           Container(
          //             padding: const EdgeInsets.all(20),
          //             decoration: BoxDecoration(
          //               color: const Color(0xFF1A1A1A),
          //               borderRadius: BorderRadius.circular(15),
          //             ),
          //             child: const Column(
          //               children: [
          //                 Text(
          //                   'SIADES - ABSENSI DIGITAL PERANGKAT DESA',
          //                   style: TextStyle(
          //                     color: Colors.white,
          //                     fontSize: 20,
          //                     fontWeight: FontWeight.w700,
          //                   ),
          //                   textAlign: TextAlign.center,
          //                 ),
          //                 SizedBox(height: 3),
          //                 Text(
          //                   'Aplikasi Absensi Digital Perangkat Desa Kabupaten Tapin',
          //                   style: TextStyle(
          //                     color: Color(0xFFBBBBBB),
          //                     fontSize: 12,
          //                   ),
          //                   textAlign: TextAlign.center,
          //                 ),
          //                 SizedBox(height: 30),
          //               ],
          //             ),
          //           ),
          //           Positioned(
          //             bottom: -20,
          //             left: 0,
          //             right: 0,
          //             child: ClipOval(
          //               child: Container(
          //                 width: 50,
          //                 height: 50,
          //                 decoration: const BoxDecoration(
          //                   color: Color(0xFF0C0C0C),
          //                 ),
          //               ),
          //             ),
          //           ),
          //           Positioned(
          //             bottom: -20,
          //             left: 0,
          //             right: 0,
          //             child: Column(
          //               children: [
          //                 Image.asset(
          //                   'images/fingerprint.png',
          //                   width: 30,
          //                   color: primaryColor,
          //                 ),
          //               ],
          //             ),
          //           )
          //         ],
          //       ),
          //       const SizedBox(height: 50),
          //       const Text(
          //         'Versi 1.0.0',
          //         style: TextStyle(
          //           color: Colors.white,
          //           fontSize: 10,
          //         ),
          //       )
          //     ],
          //   ),
          // )
        ],
      ),
    );
  }
}

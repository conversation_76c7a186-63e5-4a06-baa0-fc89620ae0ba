import 'dart:convert';
import 'dart:io';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class MyCuti extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _MyCuti();
  }
}

class _MyCuti extends State<MyCuti> {
  File? pickedFile;
  String category = 'Izin';
  DateTime? startDate;
  TimeOfDay? startTime;
  TimeOfDay? endTime;
  DateTime? endDate;
  TextEditingController descriptionController = TextEditingController();

  doSubmitPermissions() async {
    if (category.isEmpty) {
      showAlertFailed(context, 'Kategori izin tidak boleh kosong');
      return;
    } else if (startDate == null) {
      showAlertFailed(context, 'Tanggal izin tidak boleh kosong');
      return;
    } else if (startTime == null) {
      showAlertFailed(context, 'Jam mulai tidak boleh kosong');
      return;
    } else if (endTime == null) {
      showAlertFailed(context, 'Jam selesai tidak boleh kosong');
      return;
    } else if (endDate == null) {
      showAlertFailed(context, 'Tanggal selesai tidak boleh kosong');
      return;
    } else if (descriptionController.text.isEmpty) {
      showAlertFailed(context, 'Keterangan tidak boleh kosong');
      return;
    }

    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    var request =
        await http.MultipartRequest('POST', Uri.parse(API.REQUESTPERMISSION));

    request.fields['userid'] = id!;
    request.fields['category'] = category;
    request.fields['startdate'] = DateFormat('yyyy-MM-dd').format(startDate!);
    request.fields['starttime'] = "${startTime!.hour}:${startTime!.minute}:00";
    request.fields['endtime'] = "${endTime!.hour}:${endTime!.minute}:00";
    request.fields['enddate'] = DateFormat('yyyy-MM-dd').format(endDate!);
    request.fields['description'] = descriptionController!.text;

    if (pickedFile != null) {
      request.files.add(
        await http.MultipartFile.fromPath(
          'document',
          pickedFile!.path,
        ),
      );
    }

    var response = await request.send();
    var responseData = await response.stream.bytesToString();

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(responseData);

    if (json['status']) {
      Fluttertoast.showToast(msg: json['message']);
      Navigator.of(context).pop();
    } else {
      showAlertFailed(context, json['message']);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: primaryColor,
        centerTitle: true,
        title: const Text(
          'Pengajuan Izin',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(
            CupertinoIcons.back,
            color: Colors.white,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Container(
        width: double.infinity,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Kategori Izin',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 10),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFEDEDED),
                  ),
                  borderRadius: BorderRadius.circular(5),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: DropdownButton(
                  isExpanded: true,
                  underline: Container(),
                  value: category,
                  items: [
                    DropdownMenuItem(
                      child: Text(
                        'Izin',
                      ),
                      value: 'Izin',
                    ),
                    DropdownMenuItem(
                      child: Text(
                        'Cuti',
                      ),
                      value: 'Cuti',
                    ),
                    DropdownMenuItem(
                      child: Text(
                        'Sakit',
                      ),
                      value: 'Sakit',
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      category = value!;
                    });
                  },
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'Durasi Izin',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 15),
              InkWell(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: const Color(0xFFEDEDED),
                        width: 1,
                      ),
                    ),
                  ),
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        startDate != null
                            ? DateFormat('dd/MM/yyyy').format(startDate!)
                            : 'Tanggal Izin',
                        style: TextStyle(
                          fontSize: 12,
                          color: const Color(0xFFA5A5A5),
                        ),
                      ),
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 18,
                        color: const Color(0xFFA5A5A5),
                      )
                    ],
                  ),
                ),
                onTap: () async {
                  DateTime? startDate = await showDatePicker(
                    context: context,
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(
                      const Duration(
                        days: 30,
                      ),
                    ),
                    initialDate: DateTime.now(),
                  );

                  if (startDate != null) {
                    setState(() {
                      this.startDate = startDate;
                    });
                  }
                },
              ),
              const SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: InkWell(
                      child: Text(
                        startTime != null
                            ? DateFormat('H:m').format(
                                  DateTime(DateTime.now().year).copyWith(
                                      hour: startTime!.hour,
                                      minute: startTime!.minute),
                                ) +
                                ' WITA'
                            : 'Dari Jam',
                        style: TextStyle(
                          fontSize: 12,
                          color: const Color(0xFFA5A5A5),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      onTap: () async {
                        TimeOfDay? startTime = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.now(),
                        );

                        if (startTime != null) {
                          setState(() {
                            this.startTime = startTime;
                          });
                        }
                      },
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Container(
                    width: 1,
                    height: 20,
                    color: const Color(0xFFEDEDED),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: InkWell(
                      child: Text(
                        endTime != null
                            ? DateFormat('H:m').format(
                                  DateTime(DateTime.now().year).copyWith(
                                    hour: endTime!.hour,
                                    minute: endTime!.minute,
                                  ),
                                ) +
                                ' WITA'
                            : 'Sampai Jam',
                        style: TextStyle(
                          fontSize: 12,
                          color: const Color(0xFFA5A5A5),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      onTap: () async {
                        TimeOfDay? endTime = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.now(),
                        );

                        if (endTime != null) {
                          setState(() {
                            this.endTime = endTime;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Divider(
                color: const Color(0xFFEDEDED),
              ),
              const SizedBox(height: 10),
              InkWell(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: const Color(0xFFEDEDED),
                        width: 1,
                      ),
                    ),
                  ),
                  padding: const EdgeInsets.only(bottom: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        endDate != null
                            ? DateFormat('dd/MM/yyyy').format(endDate!)
                            : 'Sampai Tanggal',
                        style: TextStyle(
                          fontSize: 12,
                          color: const Color(0xFFA5A5A5),
                        ),
                      ),
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 18,
                        color: const Color(0xFFA5A5A5),
                      )
                    ],
                  ),
                ),
                onTap: () async {
                  DateTime? endDate = await showDatePicker(
                    context: context,
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(
                      const Duration(
                        days: 30,
                      ),
                    ),
                    initialDate: DateTime.now(),
                  );

                  if (endDate != null) {
                    setState(() {
                      this.endDate = endDate;
                    });
                  }
                },
              ),
              const SizedBox(height: 20),
              Text(
                'Keterangan',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 15),
              ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: TextField(
                  controller: descriptionController,
                  textAlignVertical: TextAlignVertical.top,
                  maxLines: 5,
                  decoration: InputDecoration(
                    contentPadding:
                        const EdgeInsets.all(10).copyWith(left: 15, right: 15),
                    hintText: 'Keterangan Izin',
                    hintStyle: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF707070),
                      fontWeight: FontWeight.w400,
                    ),
                    border: InputBorder.none,
                    filled: true,
                    fillColor: const Color(0xFFEDEDED),
                  ),
                  minLines: 5,
                ),
              ),
              const SizedBox(height: 20),
              InkWell(
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    color: const Color(0xFFEDEDED),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.upload_outlined,
                        color: Color(0xFF999999),
                        size: 30,
                      ),
                      const SizedBox(height: 5),
                      Text(
                        pickedFile == null
                            ? 'Upload Dokumen Pendukung (Opsional)'
                            : pickedFile!.path.split('/').last,
                        style: const TextStyle(
                          fontSize: 13,
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                onTap: () async {
                  FilePickerResult? result =
                      await FilePicker.platform.pickFiles(
                    type: FileType.custom,
                    allowedExtensions: ['pdf', 'doc', 'docx'],
                  );

                  if (result != null) {
                    setState(() {
                      pickedFile = File(result.files.single.path!);
                    });
                  }
                },
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                    ),
                    backgroundColor: primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(5),
                    ),
                  ),
                  onPressed: () async {
                    await doSubmitPermissions();
                  },
                  child: const Text(
                    'Kirim Pengajuan',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

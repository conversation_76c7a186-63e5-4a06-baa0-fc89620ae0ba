import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:siades/absence_camera.dart';
import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:detect_fake_location/detect_fake_location.dart';
import 'package:fancy_shimmer_image/fancy_shimmer_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_map_marker_animation/core/ripple_marker.dart';
import 'package:google_map_marker_animation/widgets/animarker.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';
import 'package:table_calendar/table_calendar.dart';

class MyAbsence extends StatefulWidget {
  SharedPreferences sharedPreferences;

  MyAbsence({super.key, required this.sharedPreferences});

  @override
  State<StatefulWidget> createState() {
    return _MyAbsence();
  }
}

class _MyAbsence extends State<MyAbsence> with TickerProviderStateMixin {
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();

  CameraPosition? _kGooglePlex;
  Set<Circle> circles = <Circle>{};

  late TabController _tabController;

  bool historyLoaded = false;

  bool _absenceStatus = false;
  bool _absenceStatusHome = false;
  String? _absenceAttendanceDate;
  String? _absenceAttendanceHomeDate;
  String? maxattendance;
  String? minleave;
  int? distance;
  double? longitude;
  double? latitude;
  double radius = 100;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 3, vsync: this);

    _timeString = _formatDateTime(DateTime.now());

    Timer.periodic(const Duration(seconds: 1), (timer) {
      _getTime();
    });

    Future.delayed(Duration.zero, () {
      getAbsenceStatus();
      getHistory(
        startdate: DateFormat(
          'yyyy-MM-dd',
        ).format(DateTime(DateTime.now().year, DateTime.now().month, 1)),
        enddate: DateFormat(
          'yyyy-MM-dd',
        ).format(DateTime(DateTime.now().year, DateTime.now().month + 1, 0)),
      );
    });
  }

  int _selectedTabbar = 0;
  DateTime _currentDateFilterStart = DateTime(
    DateTime.now().year,
    DateTime.now().month,
    1,
  );
  DateTime _currentDateFilterEnd = DateTime(
    DateTime.now().year,
    DateTime.now().month + 1,
    0,
  );

  // formatMonthYearInIndonesian
  String formatMonthYearInIndonesian({date}) {
    // Define Indonesian month names
    final List<String> monthNames = [
      '',
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ];

    // Get current date
    DateTime? now;
    if (date != null) {
      now = date;
    } else {
      now = DateTime.now();
    }

    // Format the date
    String month = monthNames[now!.month];
    String year = DateFormat.y().format(now);

    // Construct the formatted date string
    String formattedDate = '$month $year';

    return formattedDate;
  }

  String formatDateTodayInIndonesian({date}) {
    // Define Indonesian day names and month names
    final List<String> dayNames = [
      'Senin',
      'Selasa',
      'Rabu',
      'Kamis',
      'Jumat',
      'Sabtu',
      'Minggu',
    ];
    final List<String> monthNames = [
      '',
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ];

    // Get current date
    DateTime? now;
    if (date != null) {
      now = date;
    } else {
      now = DateTime.now();
    }

    // Format the date
    String dayName = dayNames[now!.weekday - 1];
    String day = DateFormat.d().format(now);
    String month = monthNames[now.month];
    String year = DateFormat.y().format(now);

    // Construct the formatted date string
    String formattedDate = '$dayName, $day $month $year';

    return formattedDate;
  }

  String? _timeString;

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('HH:mm:ss').format(dateTime);
  }

  void _getTime() {
    final DateTime now = DateTime.now();
    final String formattedDateTime = _formatDateTime(now);

    var timeCircular = now.second / 60;

    if (mounted) {
      setState(() {
        _timeString = formattedDateTime;
        this.timeCircular = timeCircular;
      });
    }
  }

  String compareDateTime(DateTime createdDate, DateTime updatedDate) {
    Duration difference = updatedDate.difference(createdDate);
    int hours = difference.inHours;
    int minutes = difference.inMinutes.remainder(60);
    int seconds = difference.inSeconds.remainder(60);

    String formattedDifference = DateFormat(
      'HH:mm:ss',
    ).format(DateTime(0, 0, 0, hours, minutes, seconds));
    return formattedDifference;
  }

  calculateDistanceInMeters(lat1, lon1, lat2, lon2) {
    var p = 0.017453292519943295;
    var c = cos;
    var a =
        0.5 -
        c((lat2 - lat1) * p) / 2 +
        c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p)) / 2;

    return 12742 * asin(sqrt(a)) * 1000;
  }

  getAbsenceStatus() async {
    try {
      String id = widget.sharedPreferences!.getString('id').toString();

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.ABSENCESTATUS),
        headers: {'Accept': 'application/json'},
        body: {'userid': id},
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        String maxattendance = json['data']['maxattendance'].toString();
        String minleave = json['data']['minleave'].toString();
        String absencetype = json['data']['absencetype'].toString();
        String createddate = json['data']['createddate'].toString();
        String updateddate = json['data']['updateddate'].toString();
        String longitude = json['data']['longitude'].toString();
        String latitude = json['data']['latitude'].toString();
        String radius = json['data']['radius'].toString();

        if (absencetype == 'Attendance') {
          setState(() {
            _absenceStatus = true;
            _absenceStatusHome = false;
            _absenceAttendanceDate = createddate;
            this.maxattendance = maxattendance;
            this.minleave = minleave;
            this.longitude = double.parse(longitude);
            this.latitude = double.parse(latitude);
            this.radius = double.parse(radius);
          });
        } else if (absencetype == 'Leave') {
          setState(() {
            _absenceStatus = true;
            _absenceStatusHome = true;
            _absenceAttendanceDate = createddate;
            _absenceAttendanceHomeDate = updateddate;
            this.maxattendance = maxattendance;
            this.minleave = minleave;
            this.longitude = double.parse(longitude);
            this.latitude = double.parse(latitude);
            this.radius = double.parse(radius);
          });
        } else {
          setState(() {
            this.maxattendance = maxattendance;
            this.minleave = minleave;
            this.longitude = double.parse(longitude);
            this.latitude = double.parse(latitude);
            _absenceStatus = false;
            _absenceStatusHome = false;
            this.radius = double.parse(radius);
          });
        }
      }

      bool serviceEnabled;
      LocationPermission permission;

      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        showAlertFailed(context, 'Please enable location service');
        return;
      }

      permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();

        if (permission == LocationPermission.denied) {
          showAlertFailed(context, 'Please enable location permission');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        showAlertFailed(context, 'Location permission denied forever');
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
      );

      double distance = calculateDistanceInMeters(
        latitude,
        longitude,
        position.latitude,
        position.longitude,
      );

      setState(() {
        this.distance = distance.toInt();
        _kGooglePlex = CameraPosition(
          target: LatLng(latitude!, longitude!),
          zoom: 15,
        );
        circles = <Circle>{
          Circle(
            circleId: const CircleId('currentRadius'),
            center: LatLng(latitude!, longitude!),
            radius: radius,
            strokeWidth: 1,
            strokeColor: Colors.blue,
            fillColor: Colors.blue.withOpacity(0.5),
          ),
        };
      });
    } on HandshakeException catch (e) {
      getAbsenceStatus();
    }
  }

  Uint8List convertDataUrlToUint8List(String dataUrl) {
    // Pisahkan header dari base64
    final base64String = dataUrl.split(',').last;

    // Decode base64 menjadi Uint8List
    return base64Decode(base64String);
  }

  doAbsence() async {
    try {
      bool serviceEnabled;
      LocationPermission permission;

      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        showAlertFailed(context, 'Please enable location service');
        return;
      }

      permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();

        if (permission == LocationPermission.denied) {
          showAlertFailed(context, 'Please enable location permission');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        showAlertFailed(context, 'Location permission denied forever');
        return;
      }

      if (!kIsWeb) {
        bool isFakeLocation = await DetectFakeLocation().detectFakeLocation();

        if (isFakeLocation) {
          showAlertFailed(context, 'Fake location detected');
          return;
        }
      }

      // Get village config to decide whether selfie is required
      try {
        String id = widget.sharedPreferences!.getString('id').toString();

        ModalDialog modalDialog = ModalDialog(context);
        modalDialog.showLoadingDialog();

        final confRes = await http.post(
          Uri.parse(API.ABSENCECONFIG),
          headers: {'Accept': 'application/json'},
          body: {'userid': id},
          encoding: Encoding.getByName('UTF-8'),
        );

        modalDialog.hideLoadingDialog();

        var confJson = jsonDecode(confRes.body);
        if (confJson['status'] == true) {
          bool requiresSelfie = true;
          var data = confJson['data'];
          if (data != null) {
            if (data['requiresSelfie'] != null) {
              requiresSelfie = data['requiresSelfie'] == true;
            } else if (data['absence_without_selfie'] != null) {
              requiresSelfie =
                  (data['absence_without_selfie'] == 1) ? false : true;
            }
          }

          if (!requiresSelfie) {
            // Directly process absence without selfie
            ModalDialog modalDialog2 = ModalDialog(context);
            modalDialog2.showLoadingDialog();

            Position position = await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.best,
            );

            final response = await http.post(
              Uri.parse(API.ABSENCEPROCESS),
              headers: {'Accept': 'application/json'},
              body: {
                'userid': id,
                'longitude': position.longitude.toString(),
                'latitude': position.latitude.toString(),
              },
              encoding: Encoding.getByName('UTF-8'),
            );

            modalDialog2.hideLoadingDialog();

            var json = jsonDecode(response.body);
            if (json['status'] == true) {
              Fluttertoast.showToast(msg: json['message']);
              setState(() {
                if (json['data']['absencetype'] == 'Attendance') {
                  _absenceStatus = true;
                  _absenceAttendanceDate =
                      json['data']['createddate'].toString();
                } else if (json['data']['absencetype'] == 'Leave') {
                  _absenceStatus = true;
                  _absenceStatusHome = true;
                  _absenceAttendanceHomeDate =
                      json['data']['updateddate'].toString();
                }
              });
              return; // success without selfie, stop here
            } else {
              showAlertFailed(
                context,
                json['message'] ?? 'Gagal melakukan absensi',
              );
              return;
            }
          }
          // if requiresSelfie, continue to camera flow below
        }
      } catch (_) {
        // If config API fails, continue to camera flow
      }

      XFile? image;
      String? _imageDataUrl;
      if (!kIsWeb) {
        ImagePicker picker = ImagePicker();
        image = await picker.pickImage(source: ImageSource.camera);
      } else {
        await Navigator.of(context)
            .push(MaterialPageRoute(builder: (context) => MyAbsenceCamera()))
            .then((value) {
              if (value != null) {
                _imageDataUrl = value;
              } else {
                return;
              }
            });
      }

      if (image != null || _imageDataUrl != null) {
        ModalDialog modalDialog = ModalDialog(context);
        modalDialog.showLoadingDialog();

        Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.best,
        );

        String id = widget.sharedPreferences!.getString('id').toString();

        var response = http.MultipartRequest(
          'POST',
          Uri.parse(API.ABSENCEPROCESS),
        );
        response.fields['userid'] = id;
        response.fields['longitude'] = position.longitude.toString();
        response.fields['latitude'] = position.latitude.toString();

        if (!kIsWeb) {
          response.files.add(
            await http.MultipartFile.fromPath('image', image!.path),
          );
        } else {
          final imageBytes = convertDataUrlToUint8List(_imageDataUrl!);
          response.files.add(
            await http.MultipartFile.fromBytes(
              'image',
              imageBytes,
              filename: 'snapshot.png',
            ),
          );
        }

        var responseResult = await response.send();
        var responseString = await responseResult.stream.bytesToString();
        var json = jsonDecode(responseString);

        modalDialog.hideLoadingDialog();

        if (json['status']) {
          Fluttertoast.showToast(msg: json['message']);

          setState(() {
            if (json['data']['absencetype'] == 'Attendance') {
              _absenceStatus = true;
              _absenceAttendanceDate = json['data']['createddate'].toString();
            } else if (json['data']['absencetype'] == 'Leave') {
              _absenceStatus = true;
              _absenceStatusHome = true;
              _absenceAttendanceHomeDate =
                  json['data']['updateddate'].toString();
            }
          });
        } else {
          showAlertFailed(context, json['message']);
        }
      }
    } on HandshakeException catch (e) {
      doAbsence();
    }
  }

  RefreshController historyRefreshController = RefreshController(
    initialRefresh: false,
  );

  List history = [];

  getHistory({startdate, enddate}) async {
    try {
      setState(() {
        history = [];

        historyLoaded = false;
      });

      SharedPreferences sharedPreferences = widget.sharedPreferences;
      String id = sharedPreferences!.getString('id').toString();

      ModalDialog modalDialog = ModalDialog(context);
      modalDialog.showLoadingDialog();

      final response = await http.post(
        Uri.parse(API.ABSENCEHISTORY),
        headers: {'Accept': 'application/json'},
        body: {'userid': id, 'startdate': startdate, 'enddate': enddate},
        encoding: Encoding.getByName('UTF-8'),
      );

      modalDialog.hideLoadingDialog();

      var json = jsonDecode(response.body);

      if (json['status']) {
        setState(() {
          history = json['data'];

          historyLoaded = true;
        });
      }
    } on HandshakeException catch (e) {
      getHistory(startdate: startdate, enddate: enddate);
    }
  }

  double timeCircular = 0;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        }

        Navigator.of(context).pop();
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Presensi',
            style: TextStyle(fontSize: 18, color: Colors.white),
          ),
          backgroundColor: primaryColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white, size: 20),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
        backgroundColor: Colors.white,
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                color: primaryColor,
                child: TabBar(
                  controller: _tabController,
                  labelColor: Colors.white,
                  indicatorColor: Colors.white,
                  unselectedLabelColor: Colors.white.withOpacity(0.5),
                  onTap: (value) {
                    setState(() {
                      _selectedTabbar = value;
                    });
                  },
                  tabs: const [
                    Tab(text: 'Presensi'),
                    Tab(text: 'Lokasi'),
                    Tab(text: 'Riwayat'),
                  ],
                ),
              ),
              Builder(
                builder: (context) {
                  if (_selectedTabbar == 0) {
                    return Column(
                      children: [
                        const SizedBox(height: 30),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.pin_drop_outlined,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 5),
                            Text(
                              'Anda berada pada jarak ${distance ?? 0}m dari kantor!',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 25),
                        Stack(
                          children: [
                            Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.grey.withOpacity(.1),
                                  width: 2,
                                ),
                                shape: BoxShape.circle,
                              ),
                              child: CircularProgressIndicator(
                                value: timeCircular,
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                  primaryColor,
                                ),
                              ),
                            ),
                            Positioned(
                              top: 0,
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    formatDateTodayInIndonesian(),
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  Text(
                                    _timeString!,
                                    style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 35),
                        Text(
                          formatDateTodayInIndonesian(),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.arrow_upward_outlined,
                                  color: Colors.greenAccent,
                                  size: 30,
                                ),
                                const SizedBox(height: 5),
                                const Text(
                                  'Jadwal Masuk',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  maxattendance != null ? maxattendance! : '-',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.arrow_downward_outlined,
                                  color: Colors.redAccent,
                                  size: 30,
                                ),
                                const SizedBox(height: 5),
                                const Text(
                                  'Jadwal Pulang',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                Text(
                                  minleave != null ? minleave! : '-',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    );
                  } else if (_selectedTabbar == 1) {
                    return SizedBox(
                      height: MediaQuery.of(context).size.height - 140,
                      child:
                          _kGooglePlex != null
                              ? Animarker(
                                curve: Curves.ease,
                                duration: const Duration(seconds: 2),
                                mapId: _controller.future.then<int>(
                                  (value) => value.mapId,
                                ),
                                child: GoogleMap(
                                  mapType: MapType.normal,
                                  initialCameraPosition: _kGooglePlex!,
                                  onMapCreated: (
                                    GoogleMapController controller,
                                  ) {
                                    if (!_controller.isCompleted) {
                                      _controller.complete(controller);
                                    }
                                  },
                                  circles: circles,
                                  myLocationEnabled: true,
                                  markers: {
                                    RippleMarker(
                                      markerId: const MarkerId('currentMarker'),
                                      position: LatLng(latitude!, longitude!),
                                      infoWindow: const InfoWindow(
                                        title: 'Titik Lokasi',
                                        snippet: 'Kantor',
                                      ),
                                      ripple: true,
                                    ),
                                  },
                                ),
                              )
                              : Container(),
                    );
                  } else {
                    return SizedBox(
                      height: MediaQuery.of(context).size.height - 140,
                      child: SmartRefresher(
                        controller: historyRefreshController,
                        onRefresh: () async {
                          await getHistory(
                            startdate: DateFormat(
                              'yyyy-MM-dd',
                            ).format(_currentDateFilterStart),
                            enddate: DateFormat(
                              'yyyy-MM-dd',
                            ).format(_currentDateFilterEnd),
                          );

                          historyRefreshController.refreshCompleted();
                        },
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                          ).copyWith(top: 10, bottom: 25),
                          child: Column(
                            children: [
                              const SizedBox(height: 5),
                              InkWell(
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                    horizontal: 15,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(
                                      0xFFEEEEEE,
                                    ).withOpacity(.75),
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Row(
                                          children: [
                                            const Icon(
                                              Icons.calendar_today_outlined,
                                              color: primaryColor,
                                              size: 20,
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: SingleChildScrollView(
                                                scrollDirection:
                                                    Axis.horizontal,
                                                child: Text(
                                                  '${formatDateTodayInIndonesian(date: _currentDateFilterStart)} - ${formatDateTodayInIndonesian(date: _currentDateFilterEnd)}',
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      const Icon(
                                        Icons.keyboard_arrow_down_outlined,
                                        size: 20,
                                        color: primaryColor,
                                      ),
                                    ],
                                  ),
                                ),
                                onTap: () {
                                  showModalBottomSheet(
                                    isScrollControlled: true,
                                    context: context,
                                    builder: (context) {
                                      RangeSelectionMode _rangeSelectionMode =
                                          RangeSelectionMode.toggledOn;
                                      DateTime _focusedDay = DateTime.now();
                                      DateTime? _selectedDay;
                                      DateTime? _rangeStart;
                                      DateTime? _rangeEnd;

                                      return StatefulBuilder(
                                        builder: (context, setState) {
                                          return Container(
                                            height: 500,
                                            decoration: const BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(20),
                                                topRight: Radius.circular(20),
                                              ),
                                            ),
                                            child: Column(
                                              children: [
                                                TableCalendar(
                                                  firstDay: DateTime(1970),
                                                  lastDay: DateTime.now(),
                                                  focusedDay: _focusedDay,
                                                  selectedDayPredicate:
                                                      (day) => isSameDay(
                                                        _selectedDay,
                                                        day,
                                                      ),
                                                  rangeStartDay: _rangeStart,
                                                  rangeEndDay: _rangeEnd,
                                                  rangeSelectionMode:
                                                      _rangeSelectionMode,
                                                  calendarFormat:
                                                      CalendarFormat.month,
                                                  headerStyle:
                                                      const HeaderStyle(
                                                        formatButtonVisible:
                                                            false,
                                                      ),
                                                  calendarStyle: CalendarStyle(
                                                    todayDecoration:
                                                        BoxDecoration(
                                                          border: Border.all(
                                                            color: const Color(
                                                              0xFF941EEC,
                                                            ),
                                                            width: 1,
                                                          ),
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                    todayTextStyle:
                                                        const TextStyle(
                                                          color: primaryColor,
                                                        ),
                                                  ),
                                                  onDaySelected: (
                                                    selectedDay,
                                                    focusedDay,
                                                  ) {
                                                    if (!isSameDay(
                                                      _selectedDay,
                                                      selectedDay,
                                                    )) {
                                                      setState(() {
                                                        _selectedDay =
                                                            selectedDay;
                                                        _focusedDay =
                                                            focusedDay;
                                                        _rangeStart = null;
                                                        _rangeEnd = null;
                                                        _rangeSelectionMode =
                                                            RangeSelectionMode
                                                                .toggledOff;
                                                      });
                                                    }
                                                  },
                                                  onRangeSelected: (
                                                    start,
                                                    end,
                                                    focusedDay,
                                                  ) {
                                                    setState(() {
                                                      _selectedDay = null;
                                                      _focusedDay = focusedDay;
                                                      _rangeStart = start;
                                                      _rangeEnd = end;
                                                      _rangeSelectionMode =
                                                          RangeSelectionMode
                                                              .toggledOn;
                                                    });
                                                  },
                                                  onPageChanged: (focusedDay) {
                                                    _focusedDay = focusedDay;
                                                  },
                                                ),
                                                const SizedBox(height: 20),
                                                Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        vertical: 10,
                                                        horizontal: 20,
                                                      ),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.end,
                                                    children: [
                                                      InkWell(
                                                        child: const Text(
                                                          'Batal',
                                                          style: TextStyle(
                                                            color: Color(
                                                              0xFF941EEC,
                                                            ),
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                        ),
                                                        onTap: () {
                                                          Navigator.of(
                                                            context,
                                                          ).pop();
                                                        },
                                                      ),
                                                      const SizedBox(width: 20),
                                                      InkWell(
                                                        child: const Text(
                                                          'Pilih',
                                                          style: TextStyle(
                                                            color: Color(
                                                              0xFF941EEC,
                                                            ),
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                        ),
                                                        onTap: () async {
                                                          Navigator.of(
                                                            context,
                                                          ).pop();

                                                          setState(() {
                                                            _currentDateFilterStart =
                                                                _rangeStart ??
                                                                DateTime(
                                                                  DateTime.now()
                                                                      .year,
                                                                  DateTime.now()
                                                                      .month,
                                                                  1,
                                                                );
                                                            _currentDateFilterEnd =
                                                                _rangeEnd ??
                                                                DateTime(
                                                                  DateTime.now()
                                                                      .year,
                                                                  DateTime.now()
                                                                      .month,
                                                                  0,
                                                                );
                                                          });

                                                          await getHistory(
                                                            startdate: DateFormat(
                                                              'yyyy-MM-dd',
                                                            ).format(
                                                              _currentDateFilterStart,
                                                            ),
                                                            enddate: DateFormat(
                                                              'yyyy-MM-dd',
                                                            ).format(
                                                              _currentDateFilterEnd,
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      );
                                    },
                                  );
                                },
                              ),
                              Divider(
                                thickness: .5,
                                color: const Color(0xFF555555).withOpacity(.25),
                              ),
                              Container(
                                child:
                                    historyLoaded
                                        ? history.isNotEmpty
                                            ? Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children:
                                                  history.map((e) {
                                                    return InkWell(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          const SizedBox(
                                                            height: 5,
                                                          ),
                                                          Text(
                                                            DateFormat(
                                                              'dd MMMM yyyy',
                                                            ).format(
                                                              DateTime.parse(
                                                                e['createddate'],
                                                              ),
                                                            ),
                                                            style:
                                                                const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  fontSize: 12,
                                                                ),
                                                          ),
                                                          const SizedBox(
                                                            height: 4,
                                                          ),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Expanded(
                                                                child: Row(
                                                                  children: [
                                                                    const Icon(
                                                                      Icons
                                                                          .arrow_upward_outlined,
                                                                      color: Color(
                                                                        0xFF00C53B,
                                                                      ),
                                                                      size: 20,
                                                                    ),
                                                                    const SizedBox(
                                                                      width: 5,
                                                                    ),
                                                                    Text(
                                                                      DateFormat(
                                                                        'HH:mm:ss',
                                                                      ).format(
                                                                        DateTime.parse(
                                                                          e['createddate'],
                                                                        ),
                                                                      ),
                                                                      style: const TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        color: Color(
                                                                          0xFF555555,
                                                                        ),
                                                                        fontWeight:
                                                                            FontWeight.w500,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                              const SizedBox(
                                                                width: 30,
                                                              ),
                                                              Expanded(
                                                                child: Row(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    const Icon(
                                                                      Icons
                                                                          .arrow_downward_outlined,
                                                                      color: Color(
                                                                        0xFFE64805,
                                                                      ),
                                                                      size: 20,
                                                                    ),
                                                                    const SizedBox(
                                                                      width: 5,
                                                                    ),
                                                                    Text(
                                                                      e['updateddate'] !=
                                                                              null
                                                                          ? DateFormat(
                                                                            'HH:mm:ss',
                                                                          ).format(
                                                                            DateTime.parse(
                                                                              e['updateddate'],
                                                                            ),
                                                                          )
                                                                          : '--:--:--',
                                                                      style: const TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        color: Color(
                                                                          0xFF555555,
                                                                        ),
                                                                        fontWeight:
                                                                            FontWeight.w500,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                              const SizedBox(
                                                                width: 30,
                                                              ),
                                                              Expanded(
                                                                child: Row(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    const Icon(
                                                                      Icons
                                                                          .timer_outlined,
                                                                      color: Color(
                                                                        0xFF555555,
                                                                      ),
                                                                      size: 20,
                                                                    ),
                                                                    const SizedBox(
                                                                      width: 5,
                                                                    ),
                                                                    Text(
                                                                      e['updateddate'] !=
                                                                              null
                                                                          ? compareDateTime(
                                                                            DateTime.parse(
                                                                              e['createddate'],
                                                                            ),
                                                                            DateTime.parse(
                                                                              e['updateddate'],
                                                                            ),
                                                                          )
                                                                          : '--:--:--',
                                                                      style: const TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        color: Color(
                                                                          0xFF555555,
                                                                        ),
                                                                        fontWeight:
                                                                            FontWeight.w500,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          Divider(
                                                            thickness: .5,
                                                            color: const Color(
                                                              0xFF555555,
                                                            ).withOpacity(.25),
                                                          ),
                                                        ],
                                                      ),
                                                      onTap: () {
                                                        showModalBottomSheet(
                                                          context: context,
                                                          isScrollControlled:
                                                              true,
                                                          builder: (context) {
                                                            return Container(
                                                              width:
                                                                  MediaQuery.of(
                                                                    context,
                                                                  ).size.width,
                                                              height: 375,
                                                              decoration: const BoxDecoration(
                                                                color:
                                                                    Colors
                                                                        .white,
                                                                borderRadius:
                                                                    BorderRadius.only(
                                                                      topLeft:
                                                                          Radius.circular(
                                                                            20,
                                                                          ),
                                                                      topRight:
                                                                          Radius.circular(
                                                                            20,
                                                                          ),
                                                                    ),
                                                              ),
                                                              padding:
                                                                  const EdgeInsets.symmetric(
                                                                    vertical:
                                                                        20,
                                                                    horizontal:
                                                                        20,
                                                                  ),
                                                              child: Column(
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  const Text(
                                                                    'Detail Presensi',
                                                                    style: TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                    ),
                                                                  ),
                                                                  const SizedBox(
                                                                    height: 3,
                                                                  ),
                                                                  const Divider(
                                                                    thickness:
                                                                        .5,
                                                                  ),
                                                                  const SizedBox(
                                                                    height: 10,
                                                                  ),
                                                                  Row(
                                                                    children: [
                                                                      Container(
                                                                        width:
                                                                            100,
                                                                        height:
                                                                            (e['attendanceimage'] ==
                                                                                    null
                                                                                ? 125
                                                                                : null),
                                                                        decoration: BoxDecoration(
                                                                          color: Colors
                                                                              .grey
                                                                              .withOpacity(
                                                                                .5,
                                                                              ),
                                                                          borderRadius:
                                                                              BorderRadius.circular(
                                                                                5,
                                                                              ),
                                                                        ),
                                                                        child:
                                                                            e['attendanceimage'] ==
                                                                                    null
                                                                                ? const Column(
                                                                                  mainAxisAlignment:
                                                                                      MainAxisAlignment.center,
                                                                                  crossAxisAlignment:
                                                                                      CrossAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      'No Image',
                                                                                      style: TextStyle(
                                                                                        fontSize:
                                                                                            12,
                                                                                        color:
                                                                                            Colors.white,
                                                                                      ),
                                                                                    ),
                                                                                  ],
                                                                                )
                                                                                : ClipRRect(
                                                                                  borderRadius: BorderRadius.circular(
                                                                                    5,
                                                                                  ),
                                                                                  child: FancyShimmerImage(
                                                                                    imageUrl:
                                                                                        '${API.GOOGLEAPIS}/${e['attendanceimage']}',
                                                                                    height:
                                                                                        125,
                                                                                    boxFit:
                                                                                        BoxFit.cover,
                                                                                  ),
                                                                                ),
                                                                      ),
                                                                      const SizedBox(
                                                                        width:
                                                                            15,
                                                                      ),
                                                                      Column(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment.start,
                                                                        children: [
                                                                          const Text(
                                                                            'Jam Masuk',
                                                                            style: TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w600,
                                                                              fontSize:
                                                                                  14,
                                                                            ),
                                                                          ),
                                                                          Text(
                                                                            DateFormat(
                                                                              'HH:mm:ss',
                                                                            ).format(
                                                                              DateTime.parse(
                                                                                e['createddate'],
                                                                              ),
                                                                            ),
                                                                            style: const TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w500,
                                                                              fontSize:
                                                                                  12,
                                                                            ),
                                                                          ),
                                                                          const SizedBox(
                                                                            height:
                                                                                10,
                                                                          ),
                                                                          const Text(
                                                                            'Status Absensi',
                                                                            style: TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w600,
                                                                              fontSize:
                                                                                  14,
                                                                            ),
                                                                          ),
                                                                          Text(
                                                                            e['absencestatus'] ==
                                                                                    'On Time'
                                                                                ? 'Tepat Waktu'
                                                                                : 'Terlambat',
                                                                            style: TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w500,
                                                                              fontSize:
                                                                                  12,
                                                                              color:
                                                                                  e['absencestatus'] ==
                                                                                          'On Time'
                                                                                      ? Colors.green
                                                                                      : Colors.red,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  const SizedBox(
                                                                    height: 10,
                                                                  ),
                                                                  Row(
                                                                    children: [
                                                                      Container(
                                                                        width:
                                                                            100,
                                                                        height:
                                                                            (e['leaveimage'] ==
                                                                                    null
                                                                                ? 125
                                                                                : null),
                                                                        decoration: BoxDecoration(
                                                                          color: Colors
                                                                              .grey
                                                                              .withOpacity(
                                                                                .5,
                                                                              ),
                                                                          borderRadius:
                                                                              BorderRadius.circular(
                                                                                5,
                                                                              ),
                                                                        ),
                                                                        child:
                                                                            e['leaveimage'] ==
                                                                                    null
                                                                                ? const Column(
                                                                                  mainAxisAlignment:
                                                                                      MainAxisAlignment.center,
                                                                                  crossAxisAlignment:
                                                                                      CrossAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      'No Image',
                                                                                      style: TextStyle(
                                                                                        fontSize:
                                                                                            12,
                                                                                        color:
                                                                                            Colors.white,
                                                                                      ),
                                                                                    ),
                                                                                  ],
                                                                                )
                                                                                : ClipRRect(
                                                                                  borderRadius: BorderRadius.circular(
                                                                                    5,
                                                                                  ),
                                                                                  child: FancyShimmerImage(
                                                                                    imageUrl:
                                                                                        '${API.GOOGLEAPIS}/${e['leaveimage']}',
                                                                                    height:
                                                                                        125,
                                                                                    boxFit:
                                                                                        BoxFit.cover,
                                                                                  ),
                                                                                ),
                                                                      ),
                                                                      const SizedBox(
                                                                        width:
                                                                            15,
                                                                      ),
                                                                      Column(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment.start,
                                                                        children: [
                                                                          const Text(
                                                                            'Jam Pulang',
                                                                            style: TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w600,
                                                                              fontSize:
                                                                                  14,
                                                                            ),
                                                                          ),
                                                                          Text(
                                                                            e['updateddate'] !=
                                                                                    null
                                                                                ? DateFormat(
                                                                                  'HH:mm:ss',
                                                                                ).format(
                                                                                  DateTime.parse(
                                                                                    e['updateddate'],
                                                                                  ),
                                                                                )
                                                                                : '--:--:--',
                                                                            style: const TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w500,
                                                                              fontSize:
                                                                                  12,
                                                                            ),
                                                                          ),
                                                                          const SizedBox(
                                                                            height:
                                                                                10,
                                                                          ),
                                                                          const Text(
                                                                            'Total Waktu Kerja',
                                                                            style: TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w600,
                                                                              fontSize:
                                                                                  14,
                                                                            ),
                                                                          ),
                                                                          Text(
                                                                            e['updateddate'] !=
                                                                                    null
                                                                                ? compareDateTime(
                                                                                  DateTime.parse(
                                                                                    e['createddate'],
                                                                                  ),
                                                                                  DateTime.parse(
                                                                                    e['updateddate'],
                                                                                  ),
                                                                                )
                                                                                : '--:--:--',
                                                                            style: const TextStyle(
                                                                              fontWeight:
                                                                                  FontWeight.w500,
                                                                              fontSize:
                                                                                  12,
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ],
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                    );
                                                  }).toList(),
                                            )
                                            : Container(
                                              margin: const EdgeInsets.only(
                                                top: 40,
                                              ),
                                              width:
                                                  MediaQuery.of(
                                                    context,
                                                  ).size.width,
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Image.asset(
                                                    'images/no-data.png',
                                                    width: 100,
                                                  ),
                                                  const SizedBox(height: 10),
                                                  const Text(
                                                    'Tidak ada data',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: Color(0xFF555555),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                        : Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children:
                                              [
                                                1,
                                                2,
                                                3,
                                                4,
                                                5,
                                                6,
                                                7,
                                                8,
                                                9,
                                                10,
                                              ].map((e) {
                                                return Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    const SizedBox(height: 5),
                                                    Shimmer.fromColors(
                                                      baseColor:
                                                          Colors.grey[300]!,
                                                      highlightColor:
                                                          Colors.grey[100]!,
                                                      child: Container(
                                                        width: 50,
                                                        height: 15,
                                                        decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                10,
                                                              ),
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(height: 4),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        SizedBox(
                                                          width: 75,
                                                          child: Shimmer.fromColors(
                                                            baseColor:
                                                                Colors
                                                                    .grey[300]!,
                                                            highlightColor:
                                                                Colors
                                                                    .grey[100]!,
                                                            child: Container(
                                                              width: 100,
                                                              height: 15,
                                                              decoration: BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius.circular(
                                                                      10,
                                                                    ),
                                                                color:
                                                                    Colors
                                                                        .white,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                          width: 30,
                                                        ),
                                                        SizedBox(
                                                          width: 75,
                                                          child: Shimmer.fromColors(
                                                            baseColor:
                                                                Colors
                                                                    .grey[300]!,
                                                            highlightColor:
                                                                Colors
                                                                    .grey[100]!,
                                                            child: Container(
                                                              width: 100,
                                                              height: 15,
                                                              decoration: BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius.circular(
                                                                      10,
                                                                    ),
                                                                color:
                                                                    Colors
                                                                        .white,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                          width: 30,
                                                        ),
                                                        SizedBox(
                                                          width: 75,
                                                          child: Shimmer.fromColors(
                                                            baseColor:
                                                                Colors
                                                                    .grey[300]!,
                                                            highlightColor:
                                                                Colors
                                                                    .grey[100]!,
                                                            child: Container(
                                                              width: 100,
                                                              height: 15,
                                                              decoration: BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius.circular(
                                                                      10,
                                                                    ),
                                                                color:
                                                                    Colors
                                                                        .white,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    Divider(
                                                      thickness: .5,
                                                      color: const Color(
                                                        0xFF555555,
                                                      ).withOpacity(.25),
                                                    ),
                                                  ],
                                                );
                                              }).toList(),
                                        ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
        bottomNavigationBar:
            _selectedTabbar == 0
                ? Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          color: primaryColor,
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.fingerprint_outlined,
                                color: Colors.white,
                                size: 30,
                              ),
                              SizedBox(height: 5),
                              Text(
                                'Presensi Masuk',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        onTap: () {
                          if (!_absenceStatus && !_absenceStatusHome) {
                            doAbsence();
                          }
                        },
                      ),
                    ),
                    Expanded(
                      child: InkWell(
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          color: primaryColor,
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.fingerprint_outlined,
                                color: Colors.white,
                                size: 30,
                              ),
                              SizedBox(height: 5),
                              Text(
                                'Presensi Pulang',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        onTap: () {
                          if (_absenceStatus && !_absenceStatusHome) {
                            doAbsence();
                          }
                        },
                      ),
                    ),
                  ],
                )
                : null,
      ),
    );
  }
}

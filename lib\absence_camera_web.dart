import 'package:flutter/material.dart';
import 'dart:html' as html;
import 'dart:ui_web';

class MyAbsenceCamera extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _MyAbsenceCamera();
  }
}

class _MyAbsenceCamera extends State<MyAbsenceCamera> {
  html.VideoElement? _videoElement;
  String? _imageDataUrl;

  @override
  void dispose() {
    _videoElement?.srcObject?.getTracks().forEach((track) {
      track.stop();
    });
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _initializeCamera();

    platformViewRegistry.registerViewFactory(
      'videoElement',
      (int viewId) => _videoElement!,
    );
  }

  void _initializeCamera() {
    _videoElement = html.VideoElement();
    html.window.navigator.mediaDevices
        ?.getUserMedia({'video': true}).then((stream) {
      _videoElement?.srcObject = stream;
      _videoElement?.autoplay = true;
      _videoElement?.style.width = '100%';
      _videoElement?.style.height = '100%';
      _videoElement?.setAttribute('playsinline', 'true');
      _videoElement?.setAttribute('muted', 'true');

      setState(() {});
    }).catchError((err) {
      print('Error accessing camera: $err');
    });
  }

  void _captureImage() {
    // Buat elemen canvas
    final canvas = html.CanvasElement(
        width: _videoElement!.videoWidth, height: _videoElement!.videoHeight);

    // Gambar frame video ke canvas
    canvas.context2D.drawImage(_videoElement!, 0, 0);

    // Konversi canvas ke data URL (base64)
    final dataUrl = canvas.toDataUrl("image/png");

    // Simpan data URL ke variabel untuk ditampilkan atau diunduh
    setState(() {
      _imageDataUrl = dataUrl;
    });

    Navigator.of(context).pop(_imageDataUrl);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _videoElement != null
          ? Column(
              children: [
                Expanded(
                  child: HtmlElementView(
                    viewType: 'videoElement',
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    _captureImage();
                  },
                  child: Text('Lakukan Absensi'),
                ),
                SizedBox(
                  height: 150,
                ),
              ],
            )
          : const Center(
              child: CircularProgressIndicator(),
            ),
    );
  }
}

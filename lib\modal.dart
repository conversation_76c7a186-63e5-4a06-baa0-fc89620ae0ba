import 'package:siades/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

bool hasInit = false;

class ModalDialog {
  BuildContext context;

  ModalDialog(this.context);

  void showLoadingDialog() {
    if (!hasInit) {
      showGeneralDialog(
        barrierDismissible: false,
        transitionDuration: const Duration(milliseconds: 0),
        context: context,
        pageBuilder: (
          context,
          animation,
          secondaryAnimation,
        ) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    child: LoadingAnimationWidget.threeArchedCircle(
                      color: Colors.white,
                      size: 70,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 20,
                      horizontal: 20,
                    ),
                    decoration: const BoxDecoration(
                      color: primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Image.asset(
                      'images/fingerprint.png',
                      width: 25,
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ],
          );
        },
      );
    }

    hasInit = true;
  }

  void hideLoadingDialog() {
    if (hasInit) Navigator.of(context, rootNavigator: true).pop();

    hasInit = false;
  }
}

showAlertFailed(BuildContext context, String message) {
  return showModalBottomSheet(
    elevation: 0,
    context: context,
    isScrollControlled: true,
    builder: (context) {
      return Container(
        width: double.infinity,
        height: 375,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Image.asset(
              'images/closed.gif',
              width: 150,
              fit: BoxFit.cover,
            ),
            const SizedBox(height: 10),
            const Text(
              'Terjadi Kesalahan!',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 5),
            Text(
              message,
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF555555)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 25),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE64805),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: const Text(
                  'Tutup',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ),
            )
          ],
        ),
      );
    },
  );
}

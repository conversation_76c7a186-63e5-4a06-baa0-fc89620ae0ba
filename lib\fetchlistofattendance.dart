import 'dart:convert';

import 'package:siades/api.dart';
import 'package:siades/color.dart';
import 'package:siades/modal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class MyFetchListOfAttendance extends StatefulWidget {
  String id;

  MyFetchListOfAttendance({super.key, required this.id});

  @override
  State<StatefulWidget> createState() {
    return _MyFetchListOfAttendance();
  }
}

class _MyFetchListOfAttendance extends State<MyFetchListOfAttendance> {
  Map data = {};
  String message = "Silahkan tunggu, Sedang mengambil data...";

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      getEvent();
    });
  }

  getEvent() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(Uri.parse(API.LISTOFATTENDANCESCAN),
        headers: {
          'Accept': 'application/json',
        },
        body: {
          'userid': id,
          'eventid': widget.id,
        },
        encoding: Encoding.getByName('UTF-8'));

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      setState(() {
        data = json['data'];
      });
    } else {
      setState(() {
        message = json['message'];
      });

      Future.delayed(Duration(seconds: 3), () {
        Navigator.of(context).pop();
      });
    }
  }

  doAttendance() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    String? id = sharedPreferences.getString('id');

    ModalDialog modalDialog = ModalDialog(context);
    modalDialog.showLoadingDialog();

    final response = await http.post(Uri.parse(API.LISTOFATTENDANCEPROCESS),
        headers: {'Accept': 'application/json'},
        body: {
          'userid': id,
          'eventid': widget.id,
        },
        encoding: Encoding.getByName('UTF-8'));

    modalDialog.hideLoadingDialog();

    var json = jsonDecode(response.body);

    if (json['status']) {
      Fluttertoast.showToast(msg: json['message']);

      Navigator.of(context).pop();
    } else {
      showAlertFailed(context, json['message']);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: data.isNotEmpty
          ? AppBar(
              backgroundColor: primaryColor,
              centerTitle: true,
              title: const Text(
                'Daftar Hadir',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              leading: IconButton(
                icon: const Icon(
                  CupertinoIcons.back,
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ))
          : null,
      body: Container(
        padding: const EdgeInsets.all(20),
        width: double.infinity,
        child: Column(
          mainAxisAlignment:
              data.isEmpty ? MainAxisAlignment.center : MainAxisAlignment.start,
          crossAxisAlignment: data.isEmpty
              ? CrossAxisAlignment.center
              : CrossAxisAlignment.start,
          children: data.isEmpty
              ? [
                  Center(
                    child: CircularProgressIndicator(
                      color: primaryColor,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(message),
                ]
              : [
                  Container(
                    padding: EdgeInsets.all(10).copyWith(bottom: 20, top: 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: Colors.grey[200]!,
                      ),
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              'Nama Acara',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                data['name'],
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 5),
                        Divider(
                          thickness: 0.5,
                        ),
                        SizedBox(height: 5),
                        Row(
                          children: [
                            Text(
                              'Tempat',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                data['place'],
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 5),
                        Divider(
                          thickness: 0.5,
                        ),
                        SizedBox(height: 5),
                        Row(
                          children: [
                            Text(
                              'Tanggal Kegiatan (Mulai)',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                DateFormat('dd MMMM yyyy').format(
                                  DateTime.parse(data['startevent']),
                                ),
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 5),
                        Divider(
                          thickness: 0.5,
                        ),
                        SizedBox(height: 5),
                        Row(
                          children: [
                            Text(
                              'Tanggal Kegiatan (Sampai)',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                DateFormat('dd MMMM yyyy').format(
                                  DateTime.parse(data['endevent']),
                                ),
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 25),
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                vertical: 15,
                              ),
                              backgroundColor: primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                            onPressed: () async {
                              doAttendance();
                            },
                            child: const Text(
                              'Hadir',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
        ),
      ),
    );
  }
}
